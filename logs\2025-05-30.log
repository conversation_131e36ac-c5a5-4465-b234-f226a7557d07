{"time":"2025-05-30T12:30:26.683Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:30:26.686Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:30:26.688Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:30:26.691Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:30:27.738Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:30:27.741Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:27.742Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T12:30:27.743Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:30:27.782Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:27.784Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:30:32.749Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:32.750Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:30:32.752Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:32.753Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:30:32.790Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:32.792Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:30:32.794Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:32.795Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:30:37.751Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:37.751Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:37.752Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:30:37.753Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:30:37.755Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:37.756Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:30:37.758Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:37.759Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:30:37.819Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:37.822Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:30:37.824Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:37.825Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:30:42.753Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:42.756Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:42.755Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:42.757Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:30:42.759Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:30:42.759Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T12:30:42.763Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:42.763Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:42.764Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:42.764Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:30:42.765Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:30:42.766Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:30:42.836Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:42.837Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:30:42.840Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:42.843Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T12:30:42.844Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T12:30:43.743Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T12:30:43.744Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:30:47.777Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:47.777Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:47.777Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T12:30:47.778Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:30:47.779Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:30:47.779Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T12:30:47.782Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:47.783Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:47.783Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T12:30:47.783Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:30:47.784Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:30:47.787Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:32:02.008Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:32:02.010Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:32:02.012Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:32:02.014Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:32:02.054Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:32:02.055Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:02.055Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T12:32:02.056Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:32:02.061Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:02.061Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:32:07.061Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:07.062Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:32:07.062Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:07.063Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:32:07.064Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:07.065Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:32:07.065Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:07.066Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:32:12.066Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:12.066Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:12.066Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:12.067Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:32:12.067Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:32:12.067Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:32:12.069Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:12.069Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:12.070Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:32:12.070Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:12.070Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:32:12.071Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:32:17.077Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:17.077Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:17.077Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:17.078Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:17.081Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:32:17.081Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:32:17.081Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:32:17.081Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T12:32:17.083Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:17.083Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:17.083Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:17.086Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T12:32:17.086Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:32:17.086Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:32:17.087Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T12:32:17.096Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:17.097Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:32:17.442Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T12:32:17.443Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:32:22.086Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:22.086Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:32:22.089Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:32:22.089Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:32:22.091Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:22.091Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:32:22.092Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T12:32:22.092Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:38:21.095Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:38:21.098Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:38:21.101Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:38:21.103Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:38:21.143Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T12:38:21.144Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:21.145Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T12:38:21.145Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:38:21.150Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:21.151Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:38:26.140Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:26.140Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:26.141Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:38:26.141Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:38:26.143Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:26.143Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:26.143Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:38:26.143Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:38:31.151Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:31.151Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:31.151Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:31.152Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:38:31.152Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:38:31.152Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:38:31.154Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:31.154Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:31.154Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:31.155Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:38:31.155Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:38:31.155Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:38:36.157Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:36.157Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:36.157Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:36.158Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:36.160Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:38:36.160Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:38:36.160Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T12:38:36.160Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T12:38:36.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:36.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:36.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:36.164Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:38:36.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:36.164Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:38:36.167Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:38:36.167Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T12:38:36.168Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T12:38:36.511Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T12:38:36.512Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T12:38:41.165Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:41.165Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:41.166Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T12:38:41.167Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T12:38:41.169Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T12:38:41.168Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T12:38:41.171Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:41.171Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:41.171Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T12:38:41.171Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T12:38:41.171Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T12:38:41.171Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:44:25.708Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:44:25.712Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:44:25.714Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:44:25.716Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:44:25.757Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:44:25.758Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:25.758Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T17:44:25.759Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:44:25.765Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:25.765Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:44:30.770Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:30.770Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:30.771Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:44:30.771Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:44:30.772Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:30.773Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:30.773Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:44:30.773Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:44:35.772Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:35.772Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:35.772Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:35.773Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:44:35.773Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:44:35.773Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:44:35.775Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:35.775Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:35.775Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:44:35.775Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:35.775Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:44:35.776Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:44:40.789Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:40.789Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:40.789Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:40.790Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:40.792Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:44:40.792Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:44:40.792Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:44:40.792Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:44:40.795Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:40.795Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:40.795Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:40.795Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:40.795Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:44:40.798Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T17:44:40.797Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:44:40.798Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:44:40.798Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T17:44:41.159Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T17:44:41.159Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:44:45.801Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:45.801Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:45.801Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:44:45.803Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:44:45.802Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:44:45.803Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:44:45.805Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:45.805Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:45.806Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:44:45.806Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:44:45.806Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:44:45.806Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:45:52.843Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:45:52.847Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:45:52.849Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:45:52.851Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:45:52.891Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:45:52.892Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:45:52.892Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T17:45:52.893Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:45:52.899Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:45:52.900Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:45:57.896Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:45:57.897Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:45:57.897Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:45:57.897Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:45:57.899Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:45:57.900Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:45:57.900Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:45:57.900Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:46:02.904Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:02.904Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:02.905Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:02.905Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:46:02.905Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:46:02.905Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:46:02.908Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:02.908Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:02.908Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:02.908Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:46:02.908Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:46:02.908Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:46:07.911Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:07.912Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:07.911Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:07.911Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:07.914Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:46:07.915Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:46:07.915Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:46:07.915Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:46:07.917Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:07.917Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:07.917Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:07.919Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:07.920Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T17:46:07.920Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:46:07.920Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:46:07.920Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:46:07.921Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T17:46:08.268Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T17:46:08.268Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:46:12.924Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:12.926Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:12.924Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:46:12.927Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:46:12.927Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:46:12.927Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:46:12.929Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:12.929Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:12.929Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:46:12.929Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:46:12.929Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:46:12.929Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:47:41.321Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:47:41.324Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:47:41.327Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:47:41.330Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:47:41.369Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:47:41.370Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:41.370Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T17:47:41.371Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:47:41.376Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:41.377Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:47:46.380Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:46.380Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:46.381Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:47:46.381Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:47:46.383Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:46.383Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:46.383Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:47:46.383Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:47:51.397Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:51.397Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:51.397Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:51.398Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:47:51.398Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:47:51.398Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:47:51.400Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:51.400Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:51.400Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:51.401Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:47:51.401Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:47:51.401Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:47:56.404Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:56.404Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:56.404Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:56.405Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:47:56.408Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:47:56.408Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:47:56.408Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:47:56.408Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:47:56.411Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:56.411Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:47:56.412Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:56.412Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:56.413Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:47:56.413Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:47:56.413Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:47:56.416Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T17:47:56.417Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T17:47:56.773Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T17:47:56.773Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:48:01.423Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:48:01.423Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:48:01.423Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:48:01.425Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:48:01.425Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:48:01.425Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:48:01.427Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:48:01.427Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:48:01.427Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:48:01.427Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:48:01.427Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:48:01.427Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:54:01.353Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:54:01.356Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:54:01.358Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:54:01.360Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:54:01.401Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:54:01.402Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:01.402Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T17:54:01.403Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:54:01.407Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:01.408Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:54:06.406Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:06.406Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:06.407Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:54:06.407Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:54:06.409Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:06.409Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:06.409Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:54:06.410Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:54:11.412Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:11.411Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:11.412Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:54:11.412Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:54:11.414Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:11.414Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:11.415Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:54:11.415Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:11.415Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:54:11.415Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:54:11.417Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:11.417Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:54:16.408Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:16.409Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:54:16.410Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:16.411Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:54:16.422Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:16.421Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:16.421Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:16.422Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:54:16.422Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:54:16.422Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:54:16.424Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:16.425Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:16.425Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:16.425Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:54:16.425Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:54:16.428Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T17:54:16.428Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T17:54:16.774Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T17:54:16.774Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:54:21.417Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:21.419Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:54:21.421Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:21.422Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:54:21.438Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:21.438Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:54:21.439Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:54:21.439Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:54:21.441Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:21.441Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:54:21.441Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:54:21.441Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:56:47.073Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:56:47.076Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:56:47.079Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:56:47.081Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:56:47.121Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:56:47.122Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:56:47.123Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T17:56:47.123Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:56:47.128Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:56:47.129Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:56:52.139Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:56:52.139Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:56:52.140Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:56:52.140Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:56:52.141Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:56:52.142Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:56:52.142Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:56:52.142Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:56:57.150Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:56:57.150Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:56:57.150Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:56:57.151Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:56:57.151Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:56:57.151Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:56:57.153Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:56:57.153Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:56:57.153Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:56:57.154Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:56:57.154Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:56:57.154Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:57:02.167Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:02.167Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:02.167Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:02.168Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:02.170Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:57:02.170Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:57:02.171Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:57:02.170Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:57:02.173Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:02.173Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:02.173Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:02.173Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:02.174Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:57:02.175Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:57:02.175Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:57:02.176Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T17:57:02.176Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T17:57:02.563Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T17:57:02.564Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:57:07.179Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:07.179Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:07.179Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:57:07.179Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:57:07.179Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:57:07.180Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:57:07.181Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:07.181Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:07.181Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:57:07.182Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:57:07.182Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:57:07.182Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:58:51.121Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:58:51.122Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:58:51.127Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:58:51.128Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:58:53.337Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T17:58:53.338Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T17:58:56.134Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:58:56.134Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:58:56.135Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:58:56.135Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:58:56.137Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:58:56.137Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:58:56.137Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:58:56.138Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:59:01.153Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:01.153Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:01.153Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:01.154Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:59:01.154Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:59:01.154Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:59:01.157Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:01.157Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:01.157Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:01.157Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:59:01.158Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:59:01.157Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:59:06.180Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:06.180Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:06.180Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:06.180Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:06.182Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:59:06.182Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T17:59:06.182Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:59:06.182Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:59:06.185Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:06.186Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:06.186Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T17:59:06.186Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:06.186Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:06.189Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T17:59:06.189Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:59:06.188Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:59:06.189Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T17:59:06.573Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T17:59:06.574Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T17:59:11.187Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:11.185Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:11.187Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T17:59:11.188Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T17:59:11.188Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T17:59:11.188Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T17:59:11.190Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:11.190Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:11.190Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T17:59:11.190Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T17:59:11.190Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T17:59:11.190Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:01:51.021Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:01:51.024Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:01:51.026Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:01:51.028Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:01:51.066Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:01:51.067Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:01:51.067Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:01:51.068Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:01:51.072Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:01:51.073Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:01:56.063Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:01:56.064Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:01:56.065Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:01:56.066Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:01:56.086Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:01:56.086Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:01:56.088Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:01:56.089Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:02:01.072Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:01.071Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:01.072Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:02:01.072Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:02:01.074Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:01.075Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:02:01.075Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:01.075Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:02:01.102Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:01.102Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:02:01.104Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:01.105Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:02:06.080Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:06.080Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:06.080Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:06.082Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:02:06.082Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:02:06.081Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:02:06.086Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:06.086Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:06.086Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:02:06.086Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:06.086Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:02:06.087Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:02:06.114Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:06.115Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:02:06.116Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:06.120Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:02:06.121Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:02:06.476Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: \r\nUsage: yt-dlp [OPTIONS] URL [URL...]\r\n\r\nyt-dlp: error: no such option: -\r\n"}
{"time":"2025-05-30T18:02:06.476Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:02:11.103Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:11.103Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:11.103Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:02:11.104Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:02:11.104Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:02:11.104Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:02:11.106Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:11.106Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:11.106Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:02:11.106Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:02:11.106Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:02:11.107Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:03:41.769Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:03:41.772Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:03:41.774Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:03:41.777Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:03:41.817Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:03:41.818Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:41.818Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:03:41.819Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:03:41.826Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:41.828Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:03:46.820Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:46.820Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:46.821Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:03:46.822Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:03:46.824Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:46.825Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:03:46.825Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:46.826Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:03:51.834Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:51.834Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:51.834Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:51.836Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:03:51.836Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:03:51.835Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:03:51.839Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:51.839Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:51.840Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:03:51.840Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:51.840Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:03:51.841Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:03:56.849Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:56.849Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:56.849Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:56.850Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:03:56.853Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:03:56.854Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:03:56.854Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:03:56.855Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:03:56.859Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:56.859Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:56.859Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:56.862Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:03:56.860Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:03:56.862Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:03:56.863Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:03:56.864Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:03:56.865Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:03:57.820Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:03:57.821Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:04:01.870Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:04:01.870Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:04:01.870Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:04:01.872Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:04:01.872Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:04:01.873Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:04:01.876Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:04:01.876Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:04:01.877Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:04:01.877Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:04:01.877Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:04:01.879Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:05:49.752Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:05:49.795Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:05:49.796Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:05:49.797Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:05:49.797Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:05:49.802Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:05:49.803Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:05:54.814Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:05:54.814Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:05:54.815Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:05:54.815Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:05:54.817Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:05:54.817Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:05:54.817Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:05:54.817Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:05:59.816Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:05:59.816Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:05:59.816Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:05:59.817Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:05:59.817Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:05:59.817Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:05:59.819Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:05:59.819Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:05:59.819Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:05:59.819Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:05:59.820Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:05:59.820Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:06:04.830Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:04.830Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:04.830Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:04.830Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:04.831Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:06:04.831Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:06:04.832Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:06:04.834Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:06:04.836Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:04.836Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:04.836Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:04.838Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:04.839Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:06:04.838Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:06:04.838Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:06:04.839Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:06:04.839Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:06:05.752Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:06:05.752Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:06:09.862Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:09.862Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:09.862Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:06:09.865Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:06:09.865Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:06:09.865Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:06:09.867Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:09.867Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:09.867Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:06:09.867Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:06:09.868Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:06:09.868Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:08:08.082Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:08:08.124Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:08:08.125Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:08.125Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:08:08.126Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:08:08.131Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:08.131Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:08:13.119Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:13.119Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:13.120Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:08:13.120Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:08:13.122Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:13.122Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:08:13.122Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:13.123Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:08:18.148Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:18.148Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:18.148Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:18.149Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:08:18.149Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:08:18.149Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:08:18.151Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:18.151Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:18.152Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:08:18.152Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:08:18.152Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:18.152Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:08:23.149Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:23.150Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:08:23.152Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:23.152Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:08:23.162Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:23.162Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:23.163Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:23.165Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:08:23.165Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:08:23.165Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:08:23.167Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:23.167Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:23.167Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:23.168Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:08:23.170Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:08:23.170Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:08:23.171Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:08:24.076Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:08:24.076Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:08:28.159Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:28.160Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:08:28.165Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:28.166Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:08:28.167Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:28.167Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:08:28.167Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:08:28.167Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:08:28.169Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:28.169Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:08:28.169Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:08:28.169Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:18:04.087Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:18:04.130Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:18:04.131Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:04.131Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:18:04.132Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:18:04.136Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:04.137Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:18:09.150Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:09.150Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:09.150Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:18:09.150Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:18:09.152Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:09.153Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:18:09.153Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:09.153Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:18:14.153Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:14.153Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:18:14.155Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:14.155Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:14.155Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:14.156Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:18:14.156Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:18:14.156Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:18:14.158Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:14.158Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:14.159Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:18:14.159Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:18:19.155Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:19.156Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:19.155Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:19.155Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:19.157Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:18:19.157Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:18:19.159Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:18:19.158Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:18:19.161Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:19.161Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:19.162Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:18:19.162Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:18:19.162Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:19.162Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:19.164Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:18:19.165Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:18:19.165Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:18:20.061Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:18:20.062Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:18:24.157Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:24.159Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:24.157Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:18:24.162Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:18:24.162Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:18:24.162Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:18:24.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:24.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:24.164Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:18:24.164Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:18:24.164Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:18:24.164Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:22:28.861Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:22:28.904Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:22:28.905Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:28.905Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:22:28.906Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:22:28.911Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:28.912Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:22:33.909Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:33.908Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:33.909Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:22:33.909Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:22:33.911Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:33.911Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:22:33.912Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:33.912Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:22:38.918Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:38.918Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:38.918Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:38.919Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:22:38.919Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:22:38.919Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:22:38.921Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:38.921Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:38.922Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:38.922Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:22:38.922Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:22:38.922Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:22:43.924Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:43.924Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:43.924Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:43.924Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:43.925Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:22:43.925Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:22:43.926Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:22:43.927Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:22:43.930Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:43.930Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:43.930Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:43.930Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:43.930Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:22:43.932Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:22:43.932Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:22:43.933Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:22:43.933Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:22:44.860Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:22:44.861Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:22:48.930Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:48.930Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:48.930Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:22:48.933Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:22:48.933Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:22:48.933Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:22:48.935Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:48.935Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:48.935Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:22:48.935Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:22:48.935Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:22:48.935Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:28:48.688Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:28:48.731Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:28:48.732Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:28:48.733Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:28:48.733Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:28:48.738Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:28:48.739Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:28:53.737Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:28:53.737Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:28:53.738Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:28:53.738Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:28:53.740Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:28:53.740Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:28:53.740Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:28:53.740Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:28:58.753Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:28:58.753Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:28:58.753Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:28:58.754Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:28:58.754Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:28:58.754Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:28:58.756Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:28:58.756Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:28:58.757Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:28:58.757Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:28:58.757Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:28:58.757Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:29:03.765Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:03.765Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:03.765Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:03.765Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:03.766Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:29:03.766Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:29:03.768Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:29:03.767Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:29:03.770Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:03.770Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:03.770Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:03.770Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:03.771Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:29:03.771Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:29:03.771Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:29:03.773Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:29:03.774Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:29:04.672Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:29:04.673Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:29:08.765Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:08.765Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:08.767Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:29:08.769Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:29:08.769Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:29:08.769Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:29:08.772Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:08.772Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:29:08.778Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:08.778Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:29:08.779Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:29:08.779Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:30:25.795Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:30:25.837Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-05-30T18:30:25.838Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:25.838Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-05-30T18:30:25.839Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:30:25.843Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:25.844Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:30:30.856Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:30.856Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:30.857Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:30:30.857Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:30:30.859Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:30.859Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:30:30.859Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:30.860Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:30:35.855Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:35.856Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:30:35.857Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:35.858Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:30:35.868Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:35.868Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:35.869Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:30:35.869Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:30:35.871Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:35.870Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:35.871Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:30:35.871Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:30:40.866Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:40.866Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:40.866Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:40.867Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:40.868Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Hello, world!"}}
{"time":"2025-05-30T18:30:40.870Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:30:40.869Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:30:40.867Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:30:40.872Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:40.872Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:40.874Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:30:40.874Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:40.874Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:40.875Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"yt_download"}
{"time":"2025-05-30T18:30:40.875Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:30:40.875Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
{"time":"2025-05-30T18:30:40.875Z","level":"INFO","message":"Executing tool","tool":"yt_download","args":{"url":"https://youtube.com/watch?v=abc123","audio_only":true}}
{"time":"2025-05-30T18:30:41.769Z","level":"ERROR","message":"Tool execution failed","tool":"yt_download","error":"yt-dlp error: ERROR: [youtube:truncated_id] abc123: Incomplete YouTube ID abc123. URL https://youtube.com/watch?v=abc123 looks truncated.\n"}
{"time":"2025-05-30T18:30:41.770Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":1,"maxRetries":3}
{"time":"2025-05-30T18:30:45.886Z","level":"TASK_START","message":"Task 2 started","taskId":"2","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:45.886Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:45.886Z","level":"TASK_START","message":"Task 1 started","taskId":"1","tool":"ollama_chat"}
{"time":"2025-05-30T18:30:45.887Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Parent task"}}
{"time":"2025-05-30T18:30:45.887Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"Task 1"}}
{"time":"2025-05-30T18:30:45.887Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"nonexistent","prompt":"Invalid task"}}
{"time":"2025-05-30T18:30:45.889Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:45.889Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:45.889Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-05-30T18:30:45.890Z","level":"ERROR","message":"Dependency not found: 1","taskId":"2"}
{"time":"2025-05-30T18:30:45.890Z","level":"INFO","message":"Retrying task","taskId":"2","retryCount":2,"maxRetries":3}
{"time":"2025-05-30T18:30:45.890Z","level":"INFO","message":"Retrying task","taskId":"1","retryCount":3,"maxRetries":3}
