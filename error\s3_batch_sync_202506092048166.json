{"id": "s3-sync-memory-safe-batch", "type": "batch", "description": "Memory-safe download of all photos split by device and year to prevent memory issues", "tasks": [{"id": "amanda-photos", "type": "tool", "description": "Download <PERSON>'s iPhone photos", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "amandas iPhone/", "local_path": "E:/S3/kester<PERSON>-photos/amandas iPhone/", "dry_run": false, "include": ["*.jpg", "*.png", "*.mov", "*.mp4"], "exclude": ["*.tmp", "*.log"]}}, {"id": "dylan-photos", "type": "tool", "description": "Download <PERSON>'s iPhone photos", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "<PERSON><PERSON><PERSON>'s iPhone/", "local_path": "E:/S3/kester<PERSON>-photos/d<PERSON><PERSON>'s iPhone/", "dry_run": false, "include": ["*.jpg", "*.png", "*.mov", "*.mp4"], "exclude": ["*.tmp", "*.log"]}}], "metadata": {"priority": 2, "tags": ["s3", "backup", "photos", "batch", "memory-safe"], "created_by": "user", "notes": "Memory-safe batch download split by device and year. Each task handles a manageable subset of files to prevent memory issues. Downloads to E: drive as requested."}}