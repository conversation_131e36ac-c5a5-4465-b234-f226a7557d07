{"id": "ai-tagging-2025-06-25-0001", "type": "media_tag", "description": "Generate AI-powered tags for media content", "file_path": "E:/Downloads (temp area)/Spaceballs.1987.1080p.MAX.WEB-DL.DDP.5.1.H.265-PiRaTeS/Spaceballs.1987.1080p.MAX.WEB-DL.DDP.5.1.H.265-PiRaTeS.mkv", "explain_reasoning": true, "force": false, "dependencies": ["video-transcription-example"], "metadata": {"priority": "normal", "tags": ["media", "tagging", "ai", "classification", "content-analysis"], "created_by": "dylan", "notes": "Demonstrates media tagging. Uses transcript and visual analysis to generate relevant tags. Includes reasoning explanation for transparency. Depends on transcription for better accuracy."}}