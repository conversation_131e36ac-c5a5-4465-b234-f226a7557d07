{"time":"2025-06-30T02:33:04.540Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:04.542Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:04.543Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:04.544Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:04.544Z","level":"INFO","message":"Generating summary","transcriptLength":20,"style":"bullet","model":"gpt-3.5-turbo","maxTokens":500}
{"time":"2025-06-30T02:33:06.013Z","level":"INFO","message":"Summary generated successfully","summaryLength":128,"tokensUsed":106,"processingTimeMs":1469,"model":"gpt-3.5-turbo"}
{"time":"2025-06-30T02:33:06.016Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:06.016Z","level":"INFO","message":"Generating summary","transcriptLength":57,"style":"bullet","model":"gpt-3.5-turbo","maxTokens":500}
{"time":"2025-06-30T02:33:07.217Z","level":"INFO","message":"Summary generated successfully","summaryLength":126,"tokensUsed":110,"processingTimeMs":1201,"model":"gpt-3.5-turbo"}
{"time":"2025-06-30T02:33:07.221Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.223Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.225Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.227Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.243Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.647Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-06-30T02:33:07.654Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-30T02:33:07.651Z","level":"INFO","message":"Initializing object recognizer service..."}
{"time":"2025-06-30T02:33:07.654Z","level":"ERROR","message":"Failed to initialize scene detector service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-30T02:33:07.657Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.674Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:33:07.675Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.373Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.376Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.377Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.379Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.379Z","level":"WARN","message":"OpenAI API key not configured, summarization will be disabled"}
{"time":"2025-06-30T02:34:57.493Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.494Z","level":"WARN","message":"OpenAI API key not configured, summarization will be disabled"}
{"time":"2025-06-30T02:34:57.607Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.608Z","level":"INFO","message":"Media summarization task created","taskId":2,"mediaId":1,"style":"bullet","force":false}
{"time":"2025-06-30T02:34:57.611Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.612Z","level":"INFO","message":"Starting media summarization task","taskId":1,"mediaId":999,"style":"bullet"}
{"time":"2025-06-30T02:34:57.614Z","level":"ERROR","message":"Failed to generate summary","taskId":1,"mediaId":999,"error":"No transcript found for media ID 999"}
{"time":"2025-06-30T02:34:57.616Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.616Z","level":"INFO","message":"Starting media summarization task","taskId":1,"mediaId":1,"style":"bullet"}
{"time":"2025-06-30T02:34:57.617Z","level":"INFO","message":"Summary already exists, skipping","taskId":1,"mediaId":1,"transcriptId":1}
{"time":"2025-06-30T02:34:57.620Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:57.622Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:58.069Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-06-30T02:34:58.076Z","level":"ERROR","message":"Failed to initialize scene detector service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-30T02:34:58.076Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-30T02:34:58.073Z","level":"INFO","message":"Initializing object recognizer service..."}
{"time":"2025-06-30T02:34:58.086Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:58.090Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T02:34:58.091Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.371Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.373Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.374Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.375Z","level":"WARN","message":"OpenAI API key not configured, summarization will be disabled"}
{"time":"2025-06-30T10:32:28.375Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.487Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.487Z","level":"WARN","message":"OpenAI API key not configured, summarization will be disabled"}
{"time":"2025-06-30T10:32:28.597Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.598Z","level":"INFO","message":"Media summarization task created","taskId":2,"mediaId":1,"style":"bullet","force":false}
{"time":"2025-06-30T10:32:28.601Z","level":"INFO","message":"Starting media summarization task","taskId":1,"mediaId":999,"style":"bullet"}
{"time":"2025-06-30T10:32:28.600Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.603Z","level":"ERROR","message":"Failed to generate summary","taskId":1,"mediaId":999,"error":"No transcript found for media ID 999"}
{"time":"2025-06-30T10:32:28.605Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.605Z","level":"INFO","message":"Starting media summarization task","taskId":1,"mediaId":1,"style":"bullet"}
{"time":"2025-06-30T10:32:28.607Z","level":"INFO","message":"Summary already exists, skipping","taskId":1,"mediaId":1,"transcriptId":1}
{"time":"2025-06-30T10:32:28.608Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:28.610Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:29.021Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-06-30T10:32:29.029Z","level":"ERROR","message":"Failed to initialize scene detector service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-30T10:32:29.026Z","level":"INFO","message":"Initializing object recognizer service..."}
{"time":"2025-06-30T10:32:29.029Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-30T10:32:29.032Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:29.043Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-30T10:32:29.044Z","level":"INFO","message":"Summarizer service initialized successfully"}
