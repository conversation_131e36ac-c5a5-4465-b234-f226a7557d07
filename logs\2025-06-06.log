{"time":"2025-06-06T00:39:47.802Z","level":"INFO","message":"System started","directories":{"root":"C:\\Code\\folder-watcher\\ai-drop-box","incoming":"C:\\Code\\folder-watcher\\ai-drop-box\\incoming","outputs":"C:\\Code\\folder-watcher\\ai-drop-box\\outputs","tasks":{"queued":"C:\\Code\\folder-watcher\\ai-drop-box\\tasks\\queued","completed":"C:\\Code\\folder-watcher\\ai-drop-box\\tasks\\completed","failed":"C:\\Code\\folder-watcher\\ai-drop-box\\tasks\\failed"},"logs":"C:\\Code\\folder-watcher\\ai-drop-box\\logs","dashboard":"C:\\Code\\folder-watcher\\ai-drop-box\\dashboard"}}
{"time":"2025-06-06T00:39:50.099Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Could not connect to tenant default_tenant. Are you sure it exists? Underlying error:\nError: Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-06T00:39:50.101Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-06-06T00:58:50.217Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Cannot instantiate a collection with the DefaultEmbeddingFunction. Please install @chroma-core/default-embed, or provide a different embedding function"}
{"time":"2025-06-06T00:58:50.220Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{}}
{"time":"2025-06-06T00:58:50.221Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:02:06.370Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:02:08.647Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-06T01:02:08.648Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{"name":"ChromaConnectionError"}}
{"time":"2025-06-06T01:03:15.696Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:03:17.980Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-06T01:03:17.981Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{"name":"ChromaConnectionError"}}
{"time":"2025-06-06T01:05:48.167Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:05:50.440Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-06T01:05:50.441Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{"name":"ChromaConnectionError"}}
{"time":"2025-06-06T01:08:21.976Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:08:24.259Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-06T01:08:24.261Z","level":"ERROR","message":"Failed to initialize embedding manager","error":{"name":"ChromaConnectionError"}}
{"time":"2025-06-06T01:12:32.503Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:12:32.512Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:12:48.685Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:12:48.689Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:24:37.373Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:24:37.377Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:26:27.469Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:26:27.473Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:26:47.425Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:26:47.428Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:29:27.834Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:29:27.839Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:29:34.431Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:29:34.436Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:30:50.775Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:30:50.779Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:31:16.630Z","level":"TASK_START","message":"Task test-task-string-id started","taskId":"test-task-string-id","tool":"file_operations","markdown_content":"# Test Task\n\nThis is a test task to verify string IDs work properly now."}
{"time":"2025-06-06T01:31:16.631Z","level":"INFO","message":"Retrying task","taskId":"test-task-string-id","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T01:31:21.644Z","level":"TASK_START","message":"Task test-task-string-id started","taskId":"test-task-string-id","tool":"file_operations","markdown_content":"# Test Task\n\nThis is a test task to verify string IDs work properly now."}
{"time":"2025-06-06T01:31:21.645Z","level":"INFO","message":"Retrying task","taskId":"test-task-string-id","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T01:31:26.655Z","level":"TASK_START","message":"Task test-task-string-id started","taskId":"test-task-string-id","tool":"file_operations","markdown_content":"# Test Task\n\nThis is a test task to verify string IDs work properly now."}
{"time":"2025-06-06T01:31:26.657Z","level":"INFO","message":"Retrying task","taskId":"test-task-string-id","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T01:31:31.658Z","level":"TASK_START","message":"Task test-task-string-id started","taskId":"test-task-string-id","tool":"file_operations","markdown_content":"# Test Task\n\nThis is a test task to verify string IDs work properly now."}
{"time":"2025-06-06T01:32:38.850Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:32:38.854Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:33:15.777Z","level":"TASK_START","message":"Task test-read-file-task started","taskId":"test-read-file-task","tool":"read_file","markdown_content":"# Test Read File Task\n\nThis task will test reading a file with a valid tool name."}
{"time":"2025-06-06T01:33:15.778Z","level":"INFO","message":"Executing tool","tool":"read_file","args":{"path":"C:/Users/<USER>/Sync/AI Drop Box/incoming/rename-downloads-sample.md"}}
{"time":"2025-06-06T01:33:15.780Z","level":"INFO","message":"Tool execution completed","tool":"read_file","result":{"content":"---\nid: rename-downloads-task\ntool: rename_item\nargs:\n    current_path: 'E:\\Downloads (temp area)'\nmetadata:\n    created_by: 'user'\n    priority: 2\n    tags: ['file-management', 'cleanup', 'ai-rename']\n    markdown_content: |\n        # File Rename Task\n\n        This task will use AI to automatically rename all messy files and folders\n        in the Downloads temp area with clean, properly formatted names.\n\n        ## Target Directory\n        - Path: `E:\\Downloads (temp area)`\n        - Will process all files and subdirectories\n\n        ## AI Cleaning Rules\n        The rename_item tool uses Ollama (mistral model) to clean filenames by:\n        - Using proper capitalization\n        - Removing unnecessary characters, numbers, or prefixes\n        - Replacing spaces with underscores or hyphens\n        - Keeping names descriptive but concise\n        - Following standard naming conventions\n        - Preserving file extensions\n\n        ## Expected Results\n        - All files will have clean, readable names\n        - Folders will be properly formatted\n        - No more messy download filenames with random characters\n        - File extensions will be preserved\n\n        ## Safety Features\n        - Checks that source files exist before renaming\n        - Prevents overwriting existing files\n        - Returns detailed info about old and new names\n---\n\nThis task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension.\n"}}
{"time":"2025-06-06T01:33:15.782Z","level":"TASK_COMPLETE","message":"Task test-read-file-task completed","taskId":"test-read-file-task","result":{"content":"---\nid: rename-downloads-task\ntool: rename_item\nargs:\n    current_path: 'E:\\Downloads (temp area)'\nmetadata:\n    created_by: 'user'\n    priority: 2\n    tags: ['file-management', 'cleanup', 'ai-rename']\n    markdown_content: |\n        # File Rename Task\n\n        This task will use AI to automatically rename all messy files and folders\n        in the Downloads temp area with clean, properly formatted names.\n\n        ## Target Directory\n        - Path: `E:\\Downloads (temp area)`\n        - Will process all files and subdirectories\n\n        ## AI Cleaning Rules\n        The rename_item tool uses Ollama (mistral model) to clean filenames by:\n        - Using proper capitalization\n        - Removing unnecessary characters, numbers, or prefixes\n        - Replacing spaces with underscores or hyphens\n        - Keeping names descriptive but concise\n        - Following standard naming conventions\n        - Preserving file extensions\n\n        ## Expected Results\n        - All files will have clean, readable names\n        - Folders will be properly formatted\n        - No more messy download filenames with random characters\n        - File extensions will be preserved\n\n        ## Safety Features\n        - Checks that source files exist before renaming\n        - Prevents overwriting existing files\n        - Returns detailed info about old and new names\n---\n\nThis task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension.\n"}}
{"time":"2025-06-06T01:33:15.785Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"mistral","prompt":"\ntool\ncompleted\n{\"content\":\"---\\nid: rename-downloads-task\\ntool: rename_item\\nargs:\\n    current_path: 'E:\\\\Downloads (temp area)'\\nmetadata:\\n    created_by: 'user'\\n    priority: 2\\n    tags: ['file-management', 'cleanup', 'ai-rename']\\n    markdown_content: |\\n        # File Rename Task\\n\\n        This task will use AI to automatically rename all messy files and folders\\n        in the Downloads temp area with clean, properly formatted names.\\n\\n        ## Target Directory\\n        - Path: `E:\\\\Downloads (temp area)`\\n        - Will process all files and subdirectories\\n\\n        ## AI Cleaning Rules\\n        The rename_item tool uses Ollama (mistral model) to clean filenames by:\\n        - Using proper capitalization\\n        - Removing unnecessary characters, numbers, or prefixes\\n        - Replacing spaces with underscores or hyphens\\n        - Keeping names descriptive but concise\\n        - Following standard naming conventions\\n        - Preserving file extensions\\n\\n        ## Expected Results\\n        - All files will have clean, readable names\\n        - Folders will be properly formatted\\n        - No more messy download filenames with random characters\\n        - File extensions will be preserved\\n\\n        ## Safety Features\\n        - Checks that source files exist before renaming\\n        - Prevents overwriting existing files\\n        - Returns detailed info about old and new names\\n---\\n\\nThis task will batch process all the messy files in your Downloads temp area,\\nusing AI to suggest clean, professional filenames. Perfect for organizing\\ndownloaded content that often comes with terrible default names.\\n\\n## Example Transformations\\n\\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\\n\\nThe AI will analyze each filename and suggest the most appropriate cleaned version\\nwhile preserving the essential meaning and file extension.\\n\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T01:33:15.804Z","level":"ERROR","message":"Tool execution failed","tool":"ollama_chat","error":"Ollama API error: Not Found"}
{"time":"2025-06-06T01:33:15.805Z","level":"ERROR","message":"Failed to add task embedding","taskId":"test-read-file-task","error":"Ollama API error: Not Found"}
{"time":"2025-06-06T01:33:15.806Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"test-read-file-task","error":"Ollama API error: Not Found"}
{"time":"2025-06-06T01:38:11.397Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:38:11.400Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:38:14.598Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:38:14.602Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:39:01.976Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:39:01.981Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:41:58.808Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:41:58.812Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:44:18.892Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:44:18.893Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:44:18.897Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:46:39.043Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:46:39.046Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:46:39.049Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:49:32.220Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:49:32.223Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:49:32.225Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:51:09.839Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:51:09.840Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:51:09.843Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:51:18.465Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T01:51:18.482Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:51:18.483Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)"}}
{"time":"2025-06-06T01:51:46.609Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)' -> 'E:\\<think>\nOkay, let's tackle this filename cleaning task. The user provided the name \"Downloads (temp area)\". First, I need to understand what they want. The instructions say to remove unnecessary characters, numbers, or prefixes, use proper capitalization, and make it concise.\n\nSo, the original name has parentheses with \"temp area\". The parentheses might be unnecessary. Also, \"temp area\" is probably a description of the folder's purpose, but maybe the user wants to keep it. Wait, the example given in the instructions might help. Let me check the example again. Oh right, the user wants to remove unnecessary parts. \"temp area\" might be considered redundant if the folder is just a temporary download area. Alternatively, maybe they want to keep it. But the example in the user's mind might be to simplify it. \n\nWait, the original name is \"Downloads (temp area)\". The parentheses are likely indicating that it's a temporary area. But maybe the user wants to remove the parentheses and the \"temp area\" part. Or perhaps just the parentheses. Let me think. If the folder is a temporary downloads area, maybe the clean name would be \"Downloads\" or \"Temp Downloads\". But the user might want to keep the \"temp area\" part but without the parentheses. \n\nAlternatively, maybe the parentheses are part of the original folder name, and the user wants to remove them. For example, if the folder was created with a temporary name, maybe the user wants to clean it up. So \"Downloads (temp area)\" becomes \"Downloads\" or \"Temp Downloads\". But the user might want to keep the \"temp area\" part. Wait, the instruction says to remove unnecessary characters, numbers, or prefixes. The parentheses are part of the name, but maybe they are considered unnecessary. \n\nAlternatively, maybe the user wants to keep the \"temp area\" but without the parentheses. So \"Downloads temp area\" but with proper capitalization. But the user might prefer underscores or hyphens instead of spaces. So \"Downloads-temp-area\" or \"Downloads Temp Area\". However, the original name has \"Downloads\" followed by the parentheses. Maybe the main part is \"Downloads\" and the rest is a note. So the clean name would be \"Downloads\". \n\nAlternatively, if the user wants to keep the \"temp area\" part, but without the parentheses, then \"Downloads Temp Area\" or \"Downloads-Temp-Area\". But the user might prefer to remove the entire \"temp area\" part since it's redundant. \n\nHmm, the key is to determine what's necessary. If the folder is just a temporary downloads folder, maybe the clean name is \"Downloads\" or \"Temp Downloads\". However, the user might want to keep the \"temp area\" part. Let me check the instructions again. The user says to remove unnecessary characters, numbers, or prefixes. The parentheses are characters, so they should be removed. The \"temp area\" might be considered part of the name, but if it's a temporary area, maybe the main name is \"Downloads\". \n\nSo the clean name would be \"Downloads\". But maybe the user wants to keep the \"temp area\" part. Alternatively, maybe the \"temp area\" is part of the original name, and the user wants to keep it but without the parentheses. So \"Downloads Temp Area\" or \"Downloads-Temp-Area\". \n\nI need to decide based on standard naming conventions. Typically, folders like this might be named \"Downloads\" or \"Temp Downloads\". But since the original includes \"temp area\", perhaps the user wants to keep that. However, the instruction says to remove unnecessary parts. If \"temp area\" is a description, maybe it's not necessary. \n\nAlternatively, maybe the user wants to keep the \"temp area\" but in a cleaner format. So \"Downloads Temp Area\" becomes \"Downloads-Temp-Area\" with hyphens. But the original has \"Downloads (temp area)\", so the parentheses are part of the name. Removing the parentheses and the \"temp area\" might be better. \n\nWait, the user's example might be similar to other cases. For instance, if the filename is \"Report (v2)\" the clean name would be \"Report\" or \"Report-v2\". But the user's instruction says "}
{"time":"2025-06-06T01:51:46.614Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T01:51:51.623Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:51:51.625Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)"}}
{"time":"2025-06-06T01:51:56.743Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)' -> 'E:\\<think>\nOkay, let's tackle this filename cleaning task. The user provided \"Downloads (temp area)\". First, I need to understand what they want. The instructions say to remove unnecessary characters, numbers, or prefixes, use proper capitalization, and replace spaces with underscores or hyphens.\n\nSo, the original name is \"Downloads (temp area)\". The parentheses and the content inside might be considered unnecessary. The phrase \"(temp area)\" is probably a note or a temporary label, so removing that makes sense. That leaves \"Downloads\".\n\nBut wait, maybe the user wants to keep the context? However, the instruction says to remove unnecessary parts. Since \"(temp area)\" isn't part of the main name, it's safe to remove. Also, the capitalization should be proper. \"Downloads\" is already correctly capitalized. \n\nNo spaces left after removing the parentheses part. So the cleaned name would be \"Downloads\". Let me check if there's any other characters to remove. The original has spaces, but after removing the parentheses, there are no spaces left. \n\nAlternatively, if there was a space after removing, maybe replace with an underscore. But in this case, it's just \"Downloads\". So the final answer should be \"Downloads\".\n<\\think>\n\nDownloads'"}
{"time":"2025-06-06T01:51:56.745Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T01:52:01.748Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:52:01.750Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)"}}
{"time":"2025-06-06T01:52:09.630Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)' -> 'E:\\<think>\nOkay, let's tackle this filename cleaning task. The user provided \"Downloads (temp area)\". First, I need to understand what they want. The instructions say to remove unnecessary characters, numbers, or prefixes, use underscores or hyphens instead of spaces, and keep it descriptive but concise.\n\nSo, the original name is \"Downloads (temp area)\". The parentheses and the content inside seem unnecessary. The \"(temp area)\" part is probably a note or a tag, but since the user wants to remove unnecessary parts, I should strip that. That leaves \"Downloads\".\n\nBut wait, maybe \"Downloads\" is the main name, and the \"(temp area)\" is extra. So removing the parentheses and the text inside would leave just \"Downloads\". However, sometimes people might want to keep the main part but make it cleaner. Let me check the examples again. The user wants proper capitalization, so \"Downloads\" is already correctly capitalized. \n\nAlternatively, if there's a possibility that \"temp area\" is part of the name, but the user says to remove unnecessary parts. Since \"temp area\" isn't part of the main name, it's safe to remove. So the cleaned name would be \"Downloads\". \n\nBut maybe the user wants to keep the structure but make it more standard. For example, sometimes folders are named with hyphens. However, the original doesn't have any spaces, just parentheses. So the main part is \"Downloads\", and the rest is extra. \n\nAnother thought: maybe the user wants to replace spaces with hyphens or underscores. But in this case, there are no spaces except in the parentheses. The original name has no spaces except the ones in the parentheses. So removing the parentheses and their content would result in \"Downloads\", which is already clean. \n\nI should make sure not to include any file extensions, but since there's none here, that's fine. So the final answer should be \"Downloads\".\n<\\think>\n\nDownloads'"}
{"time":"2025-06-06T01:52:09.632Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T01:52:14.646Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:52:14.650Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)"}}
{"time":"2025-06-06T01:52:21.586Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)' -> 'E:\\<think>\nOkay, let's tackle this filename cleaning task. The user provided the name \"Downloads (temp area)\". First, I need to understand what they want. They mentioned removing unnecessary characters, numbers, or prefixes, using underscores or hyphens instead of spaces, and keeping it descriptive but concise.\n\nSo, the original name is \"Downloads (temp area)\". The parentheses and the content inside might be considered unnecessary. The \"(temp area)\" part seems like a note or a temporary label, which might not be needed in the cleaned name. The main part is \"Downloads\", which is already descriptive. \n\nNext, I should check for spaces. The original has a space between \"Downloads\" and \"(temp area)\". The user wants to replace spaces with underscores or hyphens. But since the parentheses are there, maybe I should first remove the parentheses and their contents. \n\nWait, the instruction says to remove unnecessary characters, numbers, or prefixes. The \"(temp area)\" is a suffix here, maybe a prefix? Wait, it's after \"Downloads\". So it's part of the name but perhaps not needed. So removing that part would leave \"Downloads\". \n\nBut maybe the user wants to keep the main part and remove the parentheses. So \"Downloads\" would be the cleaned name. Let me check the guidelines again: remove unnecessary characters, numbers, or prefixes. The parentheses and the text inside are probably unnecessary. So the cleaned name would be \"Downloads\". \n\nAlternatively, if there's a possibility that \"(temp area)\" is part of the intended name, but since it's in parentheses, it's likely a temporary label. So the correct approach is to remove it. Therefore, the final cleaned name is \"Downloads\".\n<\\think>\n\nDownloads'"}
{"time":"2025-06-06T01:52:21.590Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"error","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed\\rename-downloads-sample.md"}
{"time":"2025-06-06T01:56:03.439Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:56:03.441Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:56:03.444Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:56:09.345Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T01:56:09.363Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:56:09.365Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T01:56:35.434Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020' -> 'E:\\Downloads (temp area)\\<think>\nOkay, let's tackle this filename: \"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1\". First, I need to figure out what the user wants. They mentioned cleaning the filename, so I should remove unnecessary parts while keeping the essential info.\n\nThe original name has \"Pinky.and.the.Brain\" which is the show title. Then there's \"S01\" for season 1. The \"1080p\" indicates the resolution, which is important. \"UPSCALED\" suggests it's upscaled, maybe from a lower resolution. \"DD.5.1\" refers to the audio format, Dolby Digital 5.1. \n\nThe user wants proper capitalization, so maybe \"Pinky and the Brain\" with spaces and correct capitalization. The \"S01\" can stay as \"S01\" since it's standard for seasons. The \"1080p\" is a resolution, so that's useful. \"UPSCALED\" might be redundant if the resolution is already 1080p, but maybe it's kept to indicate upscaled. However, sometimes people remove \"UPSCALED\" if the resolution is already high. But the user might want to keep it. \n\nThe \"DD.5.1\" is audio, so it's part of the info. The user said to remove unnecessary characters, numbers, or prefixes. The original has periods and underscores. The user wants underscores or hyphens instead of spaces. So \"Pinky.and.the.Brain\" becomes \"Pinky and the Brain\" with spaces, but maybe they prefer underscores? Wait, the user said to use underscores or hyphens instead of spaces. So maybe \"Pinky_and_the_Brain\" but the original has periods. Wait, the original has \"Pinky.and.the.Brain\" which is with periods. The user wants to remove unnecessary characters. Maybe the periods are unnecessary. So replacing periods with spaces or hyphens. \n\nWait, the user's instruction says to use underscores or hyphens instead of spaces. So maybe replace the periods with hyphens? Let me check. The original has \"Pinky.and.the.Brain\" which is title case with periods. The user wants proper capitalization, so \"Pinky and the Brain\" with spaces. But the user might prefer underscores. Wait, the user says to use underscores or hyphens instead of spaces. So maybe \"Pinky_and_the_Brain\" with underscores. But the original has periods. So maybe replace periods with underscores. \n\nBut the user's example in the query shows that the original has \"Pinky.and.the.Brain\" and the answer should be \"Pinky and the Brain\" with spaces. Wait, but the user said to use underscores or hyphens instead of spaces. So maybe \"Pinky_and_the_Brain\". But the user might prefer hyphens. Let me check the user's instruction again. \n\nThe user says: \"Uses underscores or hyphens instead of spaces\". So the correct approach is to replace spaces with underscores or hyphens. However, in the original name, the title is \"Pinky.and.the.Brain\" with periods. So maybe the user wants to replace the periods with spaces, then replace spaces with hyphens or underscores. \n\nAlternatively, the user might want to keep the title as is but clean up the rest. Let me re-examine the filename: \"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1\". The user wants to remove unnecessary characters. The \"UPSCALED\" might be redundant if the resolution is already 1080p. But maybe it's kept. The \"DD.5.1\" is audio. \n\nThe user might want to keep the season number, resolution, audio format. So the cleaned name could be \"Pinky and the Brain S01 1080p DD 5.1\" but with hyphens or underscores. Since the user said to use hyphens or underscores instead of spaces, maybe \"Pinky-and-the-Brain-S01-1080p-DD-5.1\". But the original has \"UPSCALED\" which might be unnecessary. Alternatively, maybe remove \"UPSCALED\" as it's redundant. \n\nWait, the user's instruction says to remove unnecessary characters, numbers, or prefixes. \"UPSCALED\" might be considered unnecessary if the resolution is already 1080p. So maybe remove \"UPSCALED\". So the cleaned name would be \"Pinky and the Brain S01 1080p DD 5.1\" with hyphens. \n\nBut the user might prefer underscores. Let me check the example. The user's example in the query is the original "}
{"time":"2025-06-06T01:56:35.437Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T01:56:40.437Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:56:40.439Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T01:57:04.889Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020' -> 'E:\\Downloads (temp area)\\<think>\nOkay, let's tackle this filename: \"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1\". The user wants a clean, properly formatted name. First, I need to break down the components.\n\nStarting with \"Pinky.and.the.Brain\" – that's the show title. The original has periods between the words, but maybe replacing those with spaces would be better for readability. So \"Pinky and the Brain\".\n\nNext, \"S01\" is the season number. That's standard, so I'll keep that as \"S01\".\n\nThen \"1080p\" indicates the resolution. That's a common format, so I'll leave it as \"1080p\".\n\nThe term \"UPSCALED\" is there. Since it's in uppercase, I should make it lowercase unless it's an acronym. But \"UPSCALED\" isn't an acronym, so \"upscaled\" makes sense.\n\n\"DD.5.1\" refers to the audio format, likely Dolby Digital 5.1. The periods here might be unnecessary. Maybe changing it to \"DD5.1\" would be cleaner. Alternatively, \"DD 5.1\" with a space. But the user mentioned using underscores or hyphens instead of spaces. So perhaps \"DD5.1\" is better.\n\nPutting it all together: \"Pinky and the Brain S01 1080p Upscaled DD5.1\". Wait, but the user said to use underscores or hyphens instead of spaces. So replacing spaces with underscores. Also, the original has \"UPSCALED\" which is all caps. Should it be lowercase? Yes, unless it's part of a name. So \"Upscaled\".\n\nWait, the user's example in the initial instruction shows that they might prefer hyphens. Let me check the example again. The user's example wasn't provided, but the instructions say to use underscores or hyphens. Maybe the user prefers hyphens. Let me see.\n\nAlternatively, maybe the user wants to keep the structure but clean up the formatting. Let me think: \"Pinky and the Brain S01 1080p Upscaled DD5.1\" with spaces. But the user might prefer underscores. So \"Pinky_and_the_Brain_S01_1080p_Upscaled_DD5.1\". However, the original name has \"Pinky.and.the.Brain\" with periods. Maybe the user wants to keep the title as is but clean the rest.\n\nWait, the user's instruction says to remove unnecessary characters, numbers, or prefixes. The \"S01\" is part of the season, so it's necessary. The \"108.1p\" is resolution, which is needed. The \"UPSCALED\" might be a tag, but it's part of the filename. The \"DD.5.1\" is the audio format. So maybe the user wants to keep those but format them properly.\n\nSo, the title \"Pinky.and.the.Brain\" can be converted to \"Pinky and the Brain\" with spaces. Then \"S01\" stays. \"1080p\" stays. \"UPSCALED\" becomes \"Upscaled\". \"DD.5.1\" can be \"DD5.1\" or \"DD 5.1\". The user's instruction says to use underscores or hyphens instead of spaces. So maybe replace spaces with hyphens. So \"Pinky-and-the-Brain-S01-1080p-Upscaled-DD5.1\".\n\nAlternatively, if the user prefers underscores, then \"Pinky_and_the_Brain_S01_1080p_Upscaled_DD5.1\". But the original has \"Pinky.and.the.Brain\" which is title case. Maybe the user wants to keep the title as is but clean the rest. So \"Pinky.and.the.Brain.S01.1080p.Upscaled.DD5.1\". Wait, but the user said to remove unnecessary characters. The original has periods in the title, which might be unnecessary. So converting the title to \"Pinky and the Brain\" with spaces and then hyphens or underscores.\n\nHmm, this is a bit tricky. The user wants a clean name. Let me check the example again. The user provided \"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1\" and wants it cleaned. So the main parts are the title, season, resolution, upscale, and audio. The original has periods in the title, which might be from a source like a torrent or something. So converting the title to \"Pinky and the Brain\" with spaces, then using hyphens or underscores. The \"UPSCALED\" should be lowercase. The \"DD.5.1\" can be \"DD5.1\" or \"DD 5.1\". Since the user said to use underscores or hyphens instead of spaces, perhaps \"DD5.1\" is better. So the final name would be \"Pinky-and-the-Brain-S01-1080p-Upscaled-DD5.1\".\n\nAlternatively, if the user wants to keep the title as \"Pinky.and.t"}
{"time":"2025-06-06T01:57:04.892Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T01:57:09.900Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:57:09.903Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T01:57:23.854Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020' -> 'E:\\Downloads (temp area)\\<think>\nOkay, let's tackle this filename: \"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1\". The user wants a clean name. First, I need to remove any unnecessary characters. The original has periods and some uppercase letters. Let me break it down.\n\nStarting with \"Pinky.and.the.Brain\" – that's the show title. The \"S01\" is the season number. Then \"1080p\" is the resolution. \"UPSCALED\" indicates it's upscaled, and \"DD.5.1\" refers to the audio format. \n\nThe user wants proper capitalization. So maybe \"Pinky and the Brain\" instead of the hyphens. But the original uses periods. Wait, the user said to use underscores or hyphens instead of spaces. So maybe replace the periods with underscores? Or maybe just keep the spaces but make the title proper. Let me check the example.\n\nIn the example, the assistant turned \"Pinky.and.the.Brain\" into \"Pinky and the Brain\". So they used spaces and proper capitalization. Then the rest: \"S01\" can stay as is, but maybe \"S01\" is okay. \"1080p\" is resolution, so that's okay. \"UPSCALED\" can be lowercase since it's a descriptor. \"DD.5.1\" is the audio format, so maybe keep it as \"DD 5.1\" or \"DD5.1\". But the original has a period between DD and 5.1. The user said to remove unnecessary characters. So maybe \"DD5.1\" without the period. \n\nPutting it all together: \"Pinky and the Brain S01 1080p Upscaled DD5.1\". Wait, but the user said to use underscores or hyphens instead of spaces. So maybe replace spaces with hyphens. Let me check the example again. The assistant used spaces in the example. Wait, the example given by the user in the history: the assistant returned \"Pinky and the Brain S01 1080p Upscaled DD5.1\". So they used spaces. But the user's instruction says to use underscores or hyphens instead of spaces. Hmm, maybe the user's example is conflicting. Let me recheck the user's instructions. \n\nThe user said: \"Uses underscores or hyphens instead of spaces\". So perhaps the correct approach is to replace spaces with hyphens. So \"Pinky-and-the-Brain-S01-1080p-Upscaled-DD5.1\". But the original has \"UPSCALED\" in uppercase. Should that be lowercase? The user said proper capitalization. So \"Upscaled\" would be correct. Also, \"DD.5.1\" – the period might be unnecessary. So \"DD5.1\". \n\nPutting it all together: \"Pinky-and-the-Brain-S01-1080p-Upscaled-DD5.1\". Let me check if that's concise and follows the rules. Removed the periods in the title, used hyphens instead of spaces, proper capitalization. Yes, that seems right.\n<\\think>\n\nPinky-and-the-Brain-S01-1080p-Upscaled-DD5.1.x265-EDGE2020'"}
{"time":"2025-06-06T01:57:23.856Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T01:57:28.857Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T01:57:28.861Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T01:57:44.273Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020' -> 'E:\\Downloads (temp area)\\<think>\nOkay, let's tackle this filename: \"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1\". The user wants a clean, properly formatted name. First, I need to identify the parts of the filename. \n\nThe main title is \"Pinky.and.the.Brain\", which is likely the show's name. Then there's \"S01\" indicating season 1. The \"1080p\" is the resolution, \"UPSCALED\" suggests it's upscaled, \"DD.5.1\" refers to the audio format (Dolby Digital 5.1). \n\nThe user wants to remove unnecessary characters and numbers. The original has periods and underscores, but the example response uses underscores. Wait, the original has periods in the filename. The user's example response used underscores, so maybe they prefer underscores over periods. \n\nAlso, the user mentioned to use proper capitalization. The original has periods in the title, like \"Pinky.and.the.Brain\". Should I replace the periods with spaces? The example response might have done that. Let me check the example given by the user. \n\nWait, the user hasn't provided an example, but the assistant's previous response for a similar query used underscores. So, I should replace the periods in the title with spaces and then use underscores instead of periods. \n\nSo, \"Pinky.and.the.Brain\" becomes \"Pinky and the Brain\". Then, \"S01\" can be \"S1\" since \"S01\" is redundant as \"S1\" is the same. The \"1080p\" is part of the resolution, so that stays. \"UPSCALED\" is a descriptor, so maybe keep it as \"Upscaled\". \"DD.5.1\" is the audio format, which can be written as \"DD 5.1\" or \"DD5.1\". \n\nPutting it all together: \"Pinky and the Brain S1 1080p Upscaled DD5.1\". But the user might prefer underscores. Let me check the original instruction again. The user says to use underscores or hyphens instead of spaces. So, replacing spaces with underscores. \n\nSo, \"Pinky_and_the_Brain_S1_1080p_Upscaled_DD5.1\". But the user also said to remove unnecessary numbers or prefixes. \"S01\" becomes \"S1\", which is correct. \n\nWait, the original filename has \"S01\", which is season 01. But in standard naming, it's \"S1\". So replacing \"S01\" with \"S1\" is better. \n\nAlso, the \"UPSCALED\" part. Should it be \"Upscaled\" with a capital U? The user said proper capitalization, so yes. \n\nPutting it all together: \"Pinky_and_the_Brain_S1_1080p_Upscaled_DD5.1\". That seems to fit the criteria. Let me check if there are any other unnecessary parts. The original has \"DD.5.1\", which is the audio format. It's common to write it as \"DD5.1\" without the period. \n\nSo the final cleaned name would be \"Pinky_and_the_Brain_S1_100p_Upscaled_DD5.1\"? Wait, the original says 1080p, not 100p. I must have made a typo. So correct that. \n\nFinal answer: \"Pinky_and_the_Brain_S1_1080p_Upscaled_DD5.1\"\n<\\think>\n\nPinky_and_the_Brain_S1_1080p_Upscaled_DD5.1.x265-EDGE2020'"}
{"time":"2025-06-06T01:57:44.277Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"error","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed\\rename-downloads-sample.md"}
{"time":"2025-06-06T01:58:37.162Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:58:37.162Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:58:37.164Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:58:37.163Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:58:37.165Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:58:37.167Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:58:44.272Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:58:44.273Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:58:44.276Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:58:44.276Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:58:44.278Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:58:44.280Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:58:55.279Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:58:55.280Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:58:55.282Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:58:55.283Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:58:55.283Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:58:55.285Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:10.895Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:10.896Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:10.897Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:10.899Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:10.900Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:10.901Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:18.997Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:18.997Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:18.998Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:18.998Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:19.002Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:19.002Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:35.539Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:35.541Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:35.543Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:35.543Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:35.544Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:35.546Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:43.209Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:43.209Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T01:59:43.211Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:43.211Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T01:59:43.213Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T01:59:43.214Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:00:30.365Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:00:30.365Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:00:30.367Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:00:30.367Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:00:30.371Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:00:30.371Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:01:58.809Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:01:58.809Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:01:58.835Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:01:58.834Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:01:58.836Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:01:58.836Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:01:58.840Z","level":"INFO","message":"Ollama failed, using fallback cleaning","error":"Ollama API error: Not Found"}
{"time":"2025-06-06T02:01:58.840Z","level":"INFO","message":"Ollama failed, using fallback cleaning","error":"Ollama API error: Not Found"}
{"time":"2025-06-06T02:02:04.851Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020' -> 'E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020'"}
{"time":"2025-06-06T02:02:04.853Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:02:04.859Z","level":"INFO","message":"Tool execution completed","tool":"rename_item","result":{"old_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","old_name":"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_name":"Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","suggested_name":"Pinky_and_the_Brain_S01_DD_5_1"}}
{"time":"2025-06-06T02:02:04.861Z","level":"TASK_COMPLETE","message":"Task rename-downloads-task completed","taskId":"rename-downloads-task","result":{"old_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","old_name":"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_name":"Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","suggested_name":"Pinky_and_the_Brain_S01_DD_5_1"}}
{"time":"2025-06-06T02:02:04.863Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"\ntool\ncompleted\n{\"old_path\":\"E:\\\\Downloads (temp area)\\\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020\",\"new_path\":\"E:\\\\Downloads (temp area)\\\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020\",\"old_name\":\"Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020\",\"new_name\":\"Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020\",\"suggested_name\":\"Pinky_and_the_Brain_S01_DD_5_1\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T02:02:05.242Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-06T02:02:05.243Z","level":"ERROR","message":"Failed to add task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:02:05.244Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:02:05.246Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"completed","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:02:09.872Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:02:09.874Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:02:09.876Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}
{"time":"2025-06-06T02:02:09.877Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:02:14.883Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:02:14.885Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:02:14.886Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}
{"time":"2025-06-06T02:02:14.887Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:02:19.902Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:02:19.903Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:02:19.905Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky.and.the.Brain.S01.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}
{"time":"2025-06-06T02:02:19.907Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md'"}
{"time":"2025-06-06T02:04:24.737Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:24.737Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:24.738Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:24.739Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:24.742Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:24.742Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:35.210Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:35.210Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:35.211Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:35.212Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:35.214Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:35.215Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:51.123Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:51.124Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:51.124Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:51.126Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:51.128Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:51.128Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:59.161Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:59.162Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:04:59.163Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:59.164Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:04:59.166Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:04:59.167Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:05:22.043Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:05:22.044Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:05:22.044Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:05:22.045Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:05:22.048Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:05:22.048Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:06:00.394Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:06:00.395Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:06:00.415Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:06:00.415Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:06:00.416Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:06:00.416Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:06:00.745Z","level":"INFO","message":"Ollama failed, using fallback cleaning","error":"No response from Ollama"}
{"time":"2025-06-06T02:06:00.769Z","level":"INFO","message":"Ollama failed, using fallback cleaning","error":"No response from Ollama"}
{"time":"2025-06-06T02:06:06.723Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Destination already exists: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}
{"time":"2025-06-06T02:06:06.725Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:06:06.727Z","level":"INFO","message":"Tool execution completed","tool":"rename_item","result":{"old_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020","old_name":"Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","new_name":"Pinky_and_the_Brain_S01_.x265-EDGE2020","suggested_name":"Pinky_and_the_Brain_S01_"}}
{"time":"2025-06-06T02:06:06.731Z","level":"TASK_COMPLETE","message":"Task rename-downloads-task completed","taskId":"rename-downloads-task","result":{"old_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020","old_name":"Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020","new_name":"Pinky_and_the_Brain_S01_.x265-EDGE2020","suggested_name":"Pinky_and_the_Brain_S01_"}}
{"time":"2025-06-06T02:06:06.732Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"\ntool\ncompleted\n{\"old_path\":\"E:\\\\Downloads (temp area)\\\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020\",\"new_path\":\"E:\\\\Downloads (temp area)\\\\Pinky_and_the_Brain_S01_.x265-EDGE2020\",\"old_name\":\"Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020\",\"new_name\":\"Pinky_and_the_Brain_S01_.x265-EDGE2020\",\"suggested_name\":\"Pinky_and_the_Brain_S01_\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T02:06:07.168Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-06T02:06:07.170Z","level":"ERROR","message":"Failed to add task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:06:07.171Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:06:07.173Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"completed","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:06:11.738Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:06:11.741Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:06:11.742Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:06:11.743Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:06:16.770Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:06:16.776Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:06:16.777Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:06:16.778Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:06:21.783Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:06:21.785Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:06:21.786Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:06:21.787Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md'"}
{"time":"2025-06-06T02:08:24.105Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:08:24.106Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:08:24.107Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:08:24.108Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:08:24.111Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:08:24.111Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:09:25.346Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:09:25.347Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:09:25.348Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:09:25.349Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:09:25.352Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:09:25.352Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:36.012Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:36.013Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:36.013Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:36.014Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:36.016Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:36.016Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:42.121Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:42.121Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:42.122Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:42.122Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:42.124Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:42.125Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:48.599Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:48.600Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:48.603Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:48.609Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:48.612Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:48.611Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:55.411Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:55.411Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:11:55.412Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:55.413Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:11:55.414Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:11:55.415Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:12:27.762Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:12:27.764Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:12:27.765Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:12:27.766Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:12:27.767Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:12:27.768Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:13:40.599Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:13:40.599Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:13:40.600Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:13:40.600Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:13:40.603Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:13:40.603Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:13:49.173Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:13:49.174Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:13:49.175Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:13:49.175Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:13:49.179Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:13:49.179Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:13:54.466Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:13:54.468Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:13:54.470Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:13:54.472Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:13:54.474Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:13:54.476Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:14:57.949Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:14:57.950Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:14:57.951Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:14:57.952Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:14:57.952Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:14:57.954Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:15:05.601Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:15:05.601Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:15:05.603Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:15:05.603Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:15:05.605Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:15:05.605Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:15:11.672Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:15:11.672Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:15:11.673Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:15:11.674Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:15:11.677Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:15:11.677Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:15:18.404Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:15:18.405Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:15:18.405Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:15:18.406Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:15:18.408Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:15:18.408Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:16:17.596Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:16:17.597Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:16:17.598Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:16:17.599Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:16:17.601Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:16:17.601Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:16:53.860Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:16:53.862Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:16:53.862Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:16:53.864Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:16:53.864Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:16:53.866Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:17:22.745Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:17:22.746Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:17:22.746Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:17:22.747Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:17:22.749Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:17:22.750Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:18:15.659Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:18:15.661Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:18:15.663Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:18:38.144Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:18:38.145Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:18:38.170Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:38.170Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:38.171Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:38.172Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:38.173Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:38.175Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:38.174Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:18:38.176Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:18:43.179Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:43.183Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:43.183Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:43.185Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:43.184Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:43.185Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:18:43.186Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:43.187Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:18:48.187Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:48.186Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:48.188Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:48.189Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:48.190Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:48.189Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:48.191Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:18:48.190Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:18:53.193Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:53.192Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:18:53.196Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:53.196Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}}
{"time":"2025-06-06T02:18:53.197Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:53.197Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_DD_5_1.x265-EDGE2020"}
{"time":"2025-06-06T02:18:53.199Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"error","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:18:53.199Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","error":"ENOENT: no such file or directory, unlink 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md'"}
{"time":"2025-06-06T02:20:53.344Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:20:53.346Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:20:53.348Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:21:01.476Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:21:01.481Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:21:01.485Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:21:01.486Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}}
{"time":"2025-06-06T02:21:01.499Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:21:01.500Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}}
{"time":"2025-06-06T02:21:03.727Z","level":"INFO","message":"Ollama failed, using fallback cleaning","error":"No response from Ollama"}
{"time":"2025-06-06T02:21:03.752Z","level":"INFO","message":"Ollama failed, using fallback cleaning","error":"No response from Ollama"}
{"time":"2025-06-06T02:21:09.765Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Destination already exists: E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}
{"time":"2025-06-06T02:21:09.767Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:21:09.769Z","level":"INFO","message":"Tool execution completed","tool":"rename_item","result":{"old_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020","old_name":"Pinky_and_the_Brain_S01_.x265-EDGE2020","new_name":"Pinky and the Brain S01.x265-EDGE2020","suggested_name":"Pinky and the Brain S01"}}
{"time":"2025-06-06T02:21:09.772Z","level":"TASK_COMPLETE","message":"Task rename-downloads-task completed","taskId":"rename-downloads-task","result":{"old_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020","old_name":"Pinky_and_the_Brain_S01_.x265-EDGE2020","new_name":"Pinky and the Brain S01.x265-EDGE2020","suggested_name":"Pinky and the Brain S01"}}
{"time":"2025-06-06T02:21:09.774Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"\ntool\ncompleted\n{\"old_path\":\"E:\\\\Downloads (temp area)\\\\Pinky_and_the_Brain_S01_.x265-EDGE2020\",\"new_path\":\"E:\\\\Downloads (temp area)\\\\Pinky and the Brain S01.x265-EDGE2020\",\"old_name\":\"Pinky_and_the_Brain_S01_.x265-EDGE2020\",\"new_name\":\"Pinky and the Brain S01.x265-EDGE2020\",\"suggested_name\":\"Pinky and the Brain S01\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T02:21:10.072Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-06T02:21:10.073Z","level":"ERROR","message":"Failed to add task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:21:10.074Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:21:10.076Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"completed","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:21:14.773Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:21:14.775Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}}
{"time":"2025-06-06T02:21:14.777Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}
{"time":"2025-06-06T02:21:14.778Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:21:19.785Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:21:19.788Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}}
{"time":"2025-06-06T02:21:19.790Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}
{"time":"2025-06-06T02:21:19.791Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:21:24.804Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:21:24.807Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}}
{"time":"2025-06-06T02:21:24.808Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky_and_the_Brain_S01_.x265-EDGE2020"}
{"time":"2025-06-06T02:21:24.809Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md'"}
{"time":"2025-06-06T02:27:41.713Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:27:41.714Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:27:41.714Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:27:41.715Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:27:41.718Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:27:41.723Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:30:22.252Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:30:22.252Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:30:22.254Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:30:22.253Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:30:22.256Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:30:22.256Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:32:25.094Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:32:25.096Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:32:25.099Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:32:56.127Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:32:56.127Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:32:56.149Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:32:56.149Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:32:56.150Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}}
{"time":"2025-06-06T02:32:56.151Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}}
{"time":"2025-06-06T02:33:02.601Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"ENOENT: no such file or directory, rename 'E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020' -> 'E:\\Downloads (temp area)\\cleaned_1749177176624.x265-EDGE2020'"}
{"time":"2025-06-06T02:33:02.602Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:33:02.606Z","level":"INFO","message":"Tool execution completed","tool":"rename_item","result":{"old_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\cleaned_1749177176600.x265-EDGE2020","old_name":"Pinky and the Brain S01.x265-EDGE2020","new_name":"cleaned_1749177176600.x265-EDGE2020","suggested_name":"cleaned_1749177176600"}}
{"time":"2025-06-06T02:33:02.608Z","level":"TASK_COMPLETE","message":"Task rename-downloads-task completed","taskId":"rename-downloads-task","result":{"old_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\cleaned_1749177176600.x265-EDGE2020","old_name":"Pinky and the Brain S01.x265-EDGE2020","new_name":"cleaned_1749177176600.x265-EDGE2020","suggested_name":"cleaned_1749177176600"}}
{"time":"2025-06-06T02:33:02.610Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"\ntool\ncompleted\n{\"old_path\":\"E:\\\\Downloads (temp area)\\\\Pinky and the Brain S01.x265-EDGE2020\",\"new_path\":\"E:\\\\Downloads (temp area)\\\\cleaned_1749177176600.x265-EDGE2020\",\"old_name\":\"Pinky and the Brain S01.x265-EDGE2020\",\"new_name\":\"cleaned_1749177176600.x265-EDGE2020\",\"suggested_name\":\"cleaned_1749177176600\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T02:33:05.508Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-06T02:33:05.509Z","level":"ERROR","message":"Failed to add task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:33:05.510Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:33:05.512Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"completed","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:33:07.621Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:33:07.622Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}}
{"time":"2025-06-06T02:33:07.624Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}
{"time":"2025-06-06T02:33:07.624Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:33:12.621Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:33:12.624Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}}
{"time":"2025-06-06T02:33:12.626Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}
{"time":"2025-06-06T02:33:12.626Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:33:17.635Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:33:17.637Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}}
{"time":"2025-06-06T02:33:17.638Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky and the Brain S01.x265-EDGE2020"}
{"time":"2025-06-06T02:33:17.639Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md'"}
{"time":"2025-06-06T02:34:58.230Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:34:58.231Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:34:58.232Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:34:58.233Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:34:58.234Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:34:58.235Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:35:06.610Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:35:06.611Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:35:06.611Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:35:06.612Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:35:06.615Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:35:06.615Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:35:58.990Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:35:58.992Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:35:58.994Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:36:14.226Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:36:14.229Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:36:14.255Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:36:14.258Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:36:14.261Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:36:14.263Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:36:20.600Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Destination already exists: E:\\Downloads (temp area)\\Pinky and the Brain S02.x265-EDGE2020"}
{"time":"2025-06-06T02:36:20.602Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:36:20.605Z","level":"INFO","message":"Tool execution completed","tool":"rename_item","result":{"old_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky and the Brain S02.x265-EDGE2020","old_name":"Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_name":"Pinky and the Brain S02.x265-EDGE2020","suggested_name":"Pinky and the Brain S02"}}
{"time":"2025-06-06T02:36:20.606Z","level":"TASK_COMPLETE","message":"Task rename-downloads-task completed","taskId":"rename-downloads-task","result":{"old_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_path":"E:\\Downloads (temp area)\\Pinky and the Brain S02.x265-EDGE2020","old_name":"Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020","new_name":"Pinky and the Brain S02.x265-EDGE2020","suggested_name":"Pinky and the Brain S02"}}
{"time":"2025-06-06T02:36:20.608Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"\ntool\ncompleted\n{\"old_path\":\"E:\\\\Downloads (temp area)\\\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020\",\"new_path\":\"E:\\\\Downloads (temp area)\\\\Pinky and the Brain S02.x265-EDGE2020\",\"old_name\":\"Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020\",\"new_name\":\"Pinky and the Brain S02.x265-EDGE2020\",\"suggested_name\":\"Pinky and the Brain S02\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T02:36:23.338Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-06T02:36:23.339Z","level":"ERROR","message":"Failed to add task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:36:23.340Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"rename-downloads-task","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:36:23.342Z","level":"INFO","message":"Task processing completed","taskId":"rename-downloads-task","status":"completed","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed\\rename-downloads-sample.md"}
{"time":"2025-06-06T02:36:25.623Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:36:25.625Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:36:25.626Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}
{"time":"2025-06-06T02:36:25.627Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:36:30.621Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:36:30.624Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:36:30.626Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}
{"time":"2025-06-06T02:36:30.627Z","level":"INFO","message":"Retrying task","taskId":"rename-downloads-task","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:36:35.635Z","level":"TASK_START","message":"Task rename-downloads-task started","taskId":"rename-downloads-task","tool":"rename_item","markdown_content":"This task will batch process all the messy files in your Downloads temp area,\nusing AI to suggest clean, professional filenames. Perfect for organizing\ndownloaded content that often comes with terrible default names.\n\n## Example Transformations\n\n-   `[2024] Important Document!!! (1).pdf` → `Important_Document.pdf`\n-   `IMG_20240101_MESS_001.jpg` → `Photo_2024_January.jpg`\n-   `video-file_DOWNLOADED_123.mp4` → `Video_File.mp4`\n\nThe AI will analyze each filename and suggest the most appropriate cleaned version\nwhile preserving the essential meaning and file extension."}
{"time":"2025-06-06T02:36:35.636Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}}
{"time":"2025-06-06T02:36:35.637Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Pinky.and.the.Brain.S02.1080p.UPSCALED.DD.5.1.x265-EDGE2020"}
{"time":"2025-06-06T02:36:35.638Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\rename-downloads-sample.md'"}
{"time":"2025-06-06T02:38:39.121Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:38:39.123Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:38:39.123Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:38:39.124Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:38:39.125Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:38:39.126Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:38:48.189Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:38:48.189Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:38:48.191Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:38:48.192Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:38:48.194Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:38:48.194Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:39:05.665Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:39:05.665Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:39:05.667Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:39:05.666Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:39:05.669Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:39:05.676Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:39:15.907Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:39:15.908Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:39:15.909Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:39:15.909Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:39:15.911Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:39:15.912Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:39:43.395Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:39:43.396Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T02:39:43.397Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:39:43.397Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T02:39:43.399Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:39:43.399Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T02:40:24.836Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\test-codec-removal.md"}
{"time":"2025-06-06T02:40:24.836Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\test-codec-removal.md"}
{"time":"2025-06-06T02:40:24.859Z","level":"TASK_START","message":"Task test-codec-removal started","taskId":"test-codec-removal","tool":"rename_item","markdown_content":"Test of the improved codec removal logic."}
{"time":"2025-06-06T02:40:24.859Z","level":"TASK_START","message":"Task test-codec-removal started","taskId":"test-codec-removal","tool":"rename_item","markdown_content":"Test of the improved codec removal logic."}
{"time":"2025-06-06T02:40:24.860Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}}
{"time":"2025-06-06T02:40:24.860Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}}
{"time":"2025-06-06T02:40:25.685Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Destination already exists: E:\\Downloads (temp area)\\Abbot Elementary S01 Complete-GalaxyTV[TGx]"}
{"time":"2025-06-06T02:40:25.686Z","level":"INFO","message":"Retrying task","taskId":"test-codec-removal","retryCount":1,"maxRetries":3}
{"time":"2025-06-06T02:40:25.690Z","level":"INFO","message":"Tool execution completed","tool":"rename_item","result":{"old_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]","new_path":"E:\\Downloads (temp area)\\Abbot Elementary S01 Complete-GalaxyTV[TGx]","old_name":"Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]","new_name":"Abbot Elementary S01 Complete-GalaxyTV[TGx]","suggested_name":"Abbot Elementary S01 Complete"}}
{"time":"2025-06-06T02:40:25.692Z","level":"TASK_COMPLETE","message":"Task test-codec-removal completed","taskId":"test-codec-removal","result":{"old_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]","new_path":"E:\\Downloads (temp area)\\Abbot Elementary S01 Complete-GalaxyTV[TGx]","old_name":"Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]","new_name":"Abbot Elementary S01 Complete-GalaxyTV[TGx]","suggested_name":"Abbot Elementary S01 Complete"}}
{"time":"2025-06-06T02:40:25.693Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"\ntool\ncompleted\n{\"old_path\":\"E:\\\\Downloads (temp area)\\\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]\",\"new_path\":\"E:\\\\Downloads (temp area)\\\\Abbot Elementary S01 Complete-GalaxyTV[TGx]\",\"old_name\":\"Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]\",\"new_name\":\"Abbot Elementary S01 Complete-GalaxyTV[TGx]\",\"suggested_name\":\"Abbot Elementary S01 Complete\"}","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-06T02:40:28.354Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-06T02:40:28.356Z","level":"ERROR","message":"Failed to add task embedding","taskId":"test-codec-removal","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:40:28.357Z","level":"ERROR","message":"Failed to generate task embedding","taskId":"test-codec-removal","error":"Failed to parse embedding from model response"}
{"time":"2025-06-06T02:40:28.359Z","level":"INFO","message":"Task processing completed","taskId":"test-codec-removal","status":"completed","originalPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\test-codec-removal.md","newPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed\\test-codec-removal.md"}
{"time":"2025-06-06T02:40:30.695Z","level":"TASK_START","message":"Task test-codec-removal started","taskId":"test-codec-removal","tool":"rename_item","markdown_content":"Test of the improved codec removal logic."}
{"time":"2025-06-06T02:40:30.696Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}}
{"time":"2025-06-06T02:40:30.697Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}
{"time":"2025-06-06T02:40:30.698Z","level":"INFO","message":"Retrying task","taskId":"test-codec-removal","retryCount":2,"maxRetries":3}
{"time":"2025-06-06T02:40:35.707Z","level":"TASK_START","message":"Task test-codec-removal started","taskId":"test-codec-removal","tool":"rename_item","markdown_content":"Test of the improved codec removal logic."}
{"time":"2025-06-06T02:40:35.710Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}}
{"time":"2025-06-06T02:40:35.712Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}
{"time":"2025-06-06T02:40:35.713Z","level":"INFO","message":"Retrying task","taskId":"test-codec-removal","retryCount":3,"maxRetries":3}
{"time":"2025-06-06T02:40:40.723Z","level":"TASK_START","message":"Task test-codec-removal started","taskId":"test-codec-removal","tool":"rename_item","markdown_content":"Test of the improved codec removal logic."}
{"time":"2025-06-06T02:40:40.724Z","level":"INFO","message":"Executing tool","tool":"rename_item","args":{"current_path":"E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}}
{"time":"2025-06-06T02:40:40.725Z","level":"ERROR","message":"Tool execution failed","tool":"rename_item","error":"Source path does not exist: E:\\Downloads (temp area)\\Abbott.Elementary.S01.COMPLETE.720p.DSNP.WEBRip.x264-GalaxyTV[TGx]"}
{"time":"2025-06-06T02:40:40.727Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\test-codec-removal.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\test-codec-removal.md'"}
{"time":"2025-06-06T08:55:04.652Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T08:55:04.655Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T08:55:04.658Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T08:55:12.515Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\bulk-folder-rename-sample.md"}
{"time":"2025-06-06T08:55:12.528Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\bulk-folder-rename-sample.md"}
{"time":"2025-06-06T08:55:12.523Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\bulk-folder-rename-sample.md","error":"Invalid task format: missing required tool field"}
{"time":"2025-06-06T08:55:12.550Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\bulk-folder-rename-sample.md","error":"Invalid task format: missing required tool field"}
{"time":"2025-06-06T08:56:43.637Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T08:56:43.638Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T08:56:43.641Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T08:56:50.814Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T08:56:50.816Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T08:56:50.819Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T08:56:58.078Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T08:56:58.080Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T08:56:58.083Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T09:00:33.048Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T09:00:33.050Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T09:00:33.052Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T09:00:40.865Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T09:00:40.866Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T09:00:40.868Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T09:01:16.850Z","level":"INFO","message":"System started","directories":{"root":"C:/Users/<USER>/Sync/AI Drop Box","incoming":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming","outputs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs","tasks":{"queued":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\queued","completed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\completed","failed":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\failed"},"logs":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs","dashboard":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}}
{"time":"2025-06-06T09:01:16.852Z","level":"INFO","message":"File watcher started","watchPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-06T09:01:16.854Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-06T14:20:24.385Z","level":"INFO","message":"Processing task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\bulk-folder-rename-sample.md"}
{"time":"2025-06-06T14:20:24.413Z","level":"ERROR","message":"Task processing failed","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\bulk-folder-rename-sample.md","error":"Invalid task format: missing required tool field"}
