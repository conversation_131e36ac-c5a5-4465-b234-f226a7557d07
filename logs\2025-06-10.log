{"time":"2025-06-10T02:52:57.344Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T02:52:57.343Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T02:52:57.344Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T02:52:57.345Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T02:52:57.345Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T02:52:57.345Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T02:52:57.346Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T02:52:57.348Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T02:52:57.348Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T02:52:57.349Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T02:52:57.350Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T02:52:57.368Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T02:52:57.373Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T02:52:57.374Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T02:52:57.374Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T02:52:57.375Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:11:24.605Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:11:24.606Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T03:11:24.606Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T03:11:24.607Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T03:11:24.607Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T03:11:24.608Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T03:11:24.608Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T03:11:24.611Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T03:11:24.611Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T03:11:24.612Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:11:24.612Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:11:24.612Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:11:24.612Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T03:11:24.612Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:11:24.612Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T03:11:24.613Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T03:11:24.613Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T03:11:24.613Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T03:11:24.613Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T03:11:24.631Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T03:11:24.636Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T03:11:24.637Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T03:11:24.638Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:11:24.639Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:11:39.283Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:11:39.284Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T03:11:39.284Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T03:11:39.284Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T03:11:39.284Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T03:11:39.284Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T03:11:39.285Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T03:11:39.287Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T03:11:39.287Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T03:11:39.288Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:11:39.288Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:11:39.288Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T03:11:39.288Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:11:39.288Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:11:39.288Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T03:11:39.289Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T03:11:39.289Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T03:11:39.289Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T03:11:39.289Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T03:11:39.306Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T03:11:39.313Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T03:11:39.314Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T03:11:39.315Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:11:39.315Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:14:11.249Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-10T03:14:11.281Z","level":"ERROR","message":"Shell task failed","taskId":2,"exitCode":1,"stderr":""}
{"time":"2025-06-10T03:14:11.282Z","level":"ERROR","message":"No shell_command found in task","task":{"id":3,"type":"shell","shell_command":"","status":"pending","result":null,"description":"Test missing command"}}
{"time":"2025-06-10T03:14:11.313Z","level":"ERROR","message":"Shell task failed","taskId":4,"exitCode":1,"stderr":""}
{"time":"2025-06-10T03:14:11.318Z","level":"INFO","message":"LLM task executed successfully","taskId":1,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-1-llm-output.txt"}
{"time":"2025-06-10T03:14:11.327Z","level":"ERROR","message":"Ollama API error: 500 Internal Server Error","taskId":2}
{"time":"2025-06-10T03:14:11.329Z","level":"ERROR","message":"Error executing LLM task","taskId":3,"error":"Network error"}
{"time":"2025-06-10T03:14:11.332Z","level":"INFO","message":"Code task executed successfully","taskId":1,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-1-generated-code.py","lang":"python"}
{"time":"2025-06-10T03:14:11.342Z","level":"INFO","message":"Code task executed successfully","taskId":2,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-2-generated-code.js","lang":"javascript"}
{"time":"2025-06-10T03:14:11.379Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-10T03:14:11.383Z","level":"INFO","message":"LLM task executed successfully","taskId":2,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-2-llm-output.txt"}
{"time":"2025-06-10T03:14:11.414Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-10T03:30:14.652Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:30:14.652Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T03:30:14.652Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T03:30:14.653Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T03:30:14.653Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T03:30:14.654Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T03:30:14.654Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T03:30:14.657Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T03:30:14.657Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:30:14.657Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T03:30:14.658Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:30:14.658Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:30:14.658Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T03:30:14.659Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T03:30:14.658Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:30:14.658Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T03:30:14.659Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T03:30:14.659Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T03:30:14.659Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T03:30:14.677Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T03:30:14.683Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T03:30:14.684Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T03:30:14.685Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:30:14.685Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:30:32.789Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:30:32.790Z","level":"INFO","message":"File event detected","eventType":"change","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:30:32.793Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:30:32.794Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:30:32.794Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:30:32.798Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","error":"Invalid task file format: missing frontmatter"}
{"time":"2025-06-10T03:30:32.798Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","error":"Invalid task file format: missing frontmatter"}
{"time":"2025-06-10T03:30:32.800Z","level":"INFO","message":"Moved file to error","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error\\s3-sync-batch.json"}
{"time":"2025-06-10T03:30:32.800Z","level":"INFO","message":"Moved file to error","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error\\s3-sync-batch.json"}
{"time":"2025-06-10T03:35:17.503Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:35:17.504Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T03:35:17.504Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T03:35:17.504Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T03:35:17.504Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T03:35:17.505Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T03:35:17.505Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T03:35:17.507Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T03:35:17.507Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:35:17.508Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T03:35:17.509Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T03:35:17.510Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T03:35:17.510Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T03:35:17.526Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T03:35:17.531Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T03:35:17.532Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T03:35:17.532Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:35:17.533Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:35:48.119Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:35:48.119Z","level":"INFO","message":"File event detected","eventType":"change","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:35:48.121Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:35:48.122Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:35:48.122Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:35:48.132Z","level":"INFO","message":"Task created successfully","taskId":27,"type":"batch","filename":"s3-sync-batch.json","file_hash":"6e1dd3fc7fed2dad5a4e1f9a3ebcbff011ae3fc4c32d7063d9f7c90fac691ed3"}
{"time":"2025-06-10T03:35:48.137Z","level":"INFO","message":"Task created successfully","taskId":28,"type":"batch","filename":"s3-sync-batch.json","file_hash":"6e1dd3fc7fed2dad5a4e1f9a3ebcbff011ae3fc4c32d7063d9f7c90fac691ed3"}
{"time":"2025-06-10T03:35:48.138Z","level":"INFO","message":"Moved file to archive","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\s3-sync-batch.json"}
{"time":"2025-06-10T03:35:48.139Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","error":"ENOENT: no such file or directory, rename 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json' -> 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\s3-sync-batch.json'"}
{"time":"2025-06-10T03:35:52.583Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:35:52.582Z","level":"INFO","message":"Starting task","taskId":27,"type":"batch"}
{"time":"2025-06-10T03:35:57.592Z","level":"INFO","message":"Starting task","taskId":28,"type":"batch"}
{"time":"2025-06-10T03:35:57.592Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:35:58.887Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:35:58.889Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:58.890Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:58.890Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:35:58.990Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:35:58.991Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:58.991Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:58.992Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:35:59.085Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:35:59.086Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:59.087Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:59.088Z","level":"INFO","message":"💡 MCP Recommendations received","taskId":27,"recommendations":["No similar successful tasks found. Consider breaking down the task into smaller components."]}
{"time":"2025-06-10T03:35:59.498Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:35:59.500Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:59.500Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:35:59.501Z","level":"INFO","message":"💡 MCP Recommendations received","taskId":28,"recommendations":["No similar successful tasks found. Consider breaking down the task into smaller components."]}
{"time":"2025-06-10T03:36:48.438Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:36:48.439Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T03:36:48.439Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T03:36:48.440Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T03:36:48.440Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T03:36:48.440Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T03:36:48.440Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T03:36:48.443Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T03:36:48.443Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T03:36:48.443Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T03:36:48.445Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T03:36:48.444Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T03:36:48.445Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T03:36:48.465Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T03:36:48.472Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T03:36:48.473Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T03:36:48.474Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:36:48.475Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:37:54.258Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:37:54.259Z","level":"INFO","message":"File event detected","eventType":"change","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:37:54.261Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:37:54.261Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:37:54.261Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:37:54.263Z","level":"INFO","message":"Duplicate file detected, moving to archive and skipping","filename":"s3-sync-batch.json","file_hash":"6e1dd3fc7fed2dad5a4e1f9a3ebcbff011ae3fc4c32d7063d9f7c90fac691ed3"}
{"time":"2025-06-10T03:37:54.267Z","level":"INFO","message":"Duplicate file detected, moving to archive and skipping","filename":"s3-sync-batch.json","file_hash":"6e1dd3fc7fed2dad5a4e1f9a3ebcbff011ae3fc4c32d7063d9f7c90fac691ed3"}
{"time":"2025-06-10T03:37:54.291Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","error":"ENOENT: no such file or directory, rename 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json' -> 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\s3-sync-batch.json'"}
{"time":"2025-06-10T03:38:40.011Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:38:40.012Z","level":"INFO","message":"File event detected","eventType":"change","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:38:40.036Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:38:40.036Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-batch.json"}
{"time":"2025-06-10T03:38:40.036Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json"}
{"time":"2025-06-10T03:38:40.039Z","level":"INFO","message":"Duplicate file detected, moving to archive and skipping","filename":"s3-sync-batch.json","file_hash":"dce833e063a66fc221e29e8bb73c6f4e52ec27bc7f083c80173d08fba5140db7"}
{"time":"2025-06-10T03:38:40.038Z","level":"INFO","message":"Duplicate file detected, moving to archive and skipping","filename":"s3-sync-batch.json","file_hash":"dce833e063a66fc221e29e8bb73c6f4e52ec27bc7f083c80173d08fba5140db7"}
{"time":"2025-06-10T03:38:40.051Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-batch.json","error":"ENOENT: no such file or directory, rename 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-batch.json' -> 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\s3-sync-batch.json'"}
{"time":"2025-06-10T03:41:43.857Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-10T03:41:43.857Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:41:43.857Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-10T03:41:43.858Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-10T03:41:43.858Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-10T03:41:43.858Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-10T03:41:43.858Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-10T03:41:43.861Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-10T03:41:43.861Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-10T03:41:43.862Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:41:43.863Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:41:43.863Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T03:41:43.863Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:41:43.863Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-10T03:41:43.864Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-10T03:41:43.863Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:41:43.864Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-10T03:41:43.864Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-10T03:41:43.864Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-10T03:41:43.883Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-10T03:41:43.888Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-10T03:41:43.889Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\folder-watcher/src/mcp/live-dashboard.html"}
{"time":"2025-06-10T03:41:43.890Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-10T03:41:43.891Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-10T03:41:56.049Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-daily.json"}
{"time":"2025-06-10T03:41:56.050Z","level":"INFO","message":"File event detected","eventType":"change","filename":"s3-sync-daily.json"}
{"time":"2025-06-10T03:41:56.053Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"s3-sync-daily.json"}
{"time":"2025-06-10T03:41:56.054Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-daily.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-daily.json"}
{"time":"2025-06-10T03:41:56.054Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-daily.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-daily.json"}
{"time":"2025-06-10T03:41:56.077Z","level":"INFO","message":"Task created successfully","taskId":29,"type":"batch","filename":"s3-sync-daily.json","file_hash":"a2cb19ae8e9698a47d2e5565fa078209ef39d81097aab7a0672e5a2200198def"}
{"time":"2025-06-10T03:41:56.083Z","level":"INFO","message":"Task created successfully","taskId":30,"type":"batch","filename":"s3-sync-daily.json","file_hash":"a2cb19ae8e9698a47d2e5565fa078209ef39d81097aab7a0672e5a2200198def"}
{"time":"2025-06-10T03:41:56.085Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\s3-sync-daily.json","error":"ENOENT: no such file or directory, rename 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-daily.json' -> 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\s3-sync-daily.json'"}
{"time":"2025-06-10T03:41:56.085Z","level":"INFO","message":"Moved file to archive","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\s3-sync-daily.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\s3-sync-daily.json"}
{"time":"2025-06-10T03:41:58.904Z","level":"INFO","message":"Starting task","taskId":29,"type":"batch"}
{"time":"2025-06-10T03:41:58.905Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:42:01.450Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:42:01.451Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:01.452Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:01.452Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:42:01.565Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:42:01.566Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:01.567Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:01.568Z","level":"INFO","message":"💡 MCP Recommendations received","taskId":29,"recommendations":["No similar successful tasks found. Consider breaking down the task into smaller components."]}
{"time":"2025-06-10T03:42:03.895Z","level":"INFO","message":"Starting task","taskId":30,"type":"batch"}
{"time":"2025-06-10T03:42:03.895Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:42:04.004Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:42:04.005Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:04.005Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:04.006Z","level":"INFO","message":"Executing tool","tool":"ollama_chat","args":{"model":"qwen3:8b","prompt":"Memory-safe download of all photos split by device and year to prevent memory issues","system":"You are an embedding model. Convert the input text into a vector representation."}}
{"time":"2025-06-10T03:42:04.110Z","level":"INFO","message":"Tool execution completed","tool":"ollama_chat","result":{"response":""}}
{"time":"2025-06-10T03:42:04.111Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:04.111Z","level":"ERROR","message":"Failed to find similar tasks","error":"Failed to parse embedding from model response"}
{"time":"2025-06-10T03:42:04.112Z","level":"INFO","message":"💡 MCP Recommendations received","taskId":30,"recommendations":["No similar successful tasks found. Consider breaking down the task into smaller components."]}
{"time":"2025-06-10T03:43:25.778Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-10T03:43:25.779Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-10T03:43:25.779Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-10T03:43:25.779Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-10T03:43:25.779Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-10T11:13:26.229Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-10T11:13:26.257Z","level":"ERROR","message":"Shell task failed","taskId":2,"exitCode":1,"stderr":""}
{"time":"2025-06-10T11:13:26.259Z","level":"ERROR","message":"No shell_command found in task","task":{"id":3,"type":"shell","shell_command":"","status":"pending","result":null,"description":"Test missing command"}}
{"time":"2025-06-10T11:13:26.288Z","level":"ERROR","message":"Shell task failed","taskId":4,"exitCode":1,"stderr":""}
{"time":"2025-06-10T11:13:26.293Z","level":"INFO","message":"LLM task executed successfully","taskId":1,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-1-llm-output.txt"}
{"time":"2025-06-10T11:13:26.295Z","level":"ERROR","message":"Ollama API error: 500 Internal Server Error","taskId":2}
{"time":"2025-06-10T11:13:26.296Z","level":"ERROR","message":"Error executing LLM task","taskId":3,"error":"Network error"}
{"time":"2025-06-10T11:13:26.299Z","level":"INFO","message":"Code task executed successfully","taskId":1,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-1-generated-code.py","lang":"python"}
{"time":"2025-06-10T11:13:26.308Z","level":"INFO","message":"Code task executed successfully","taskId":2,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-2-generated-code.js","lang":"javascript"}
{"time":"2025-06-10T11:13:26.341Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-10T11:13:26.344Z","level":"INFO","message":"LLM task executed successfully","taskId":2,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-2-llm-output.txt"}
{"time":"2025-06-10T11:13:26.373Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
