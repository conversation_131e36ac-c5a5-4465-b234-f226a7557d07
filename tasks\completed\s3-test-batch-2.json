{"id": "s3-test-batch-small", "type": "batch", "description": "Small test batch to verify S3 sync works with batch tasks", "tasks": [{"id": "test-dylan-2025-may", "type": "tool", "description": "Test: Download <PERSON>'s iPhone May 2025 photos (small subset)", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "<PERSON><PERSON><PERSON>'s iPhone/2025/05/", "local_path": "C:/Photos/Test/Dylan_2025_May", "dry_run": true, "include": ["*.jpg", "*.png", "*.mov"], "exclude": ["*.tmp", "*.log"]}}, {"id": "test-dylan-2025-june", "type": "tool", "description": "Test: Download <PERSON>'s iPhone June 2025 photos (small subset)", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "<PERSON><PERSON><PERSON>'s iPhone/2025/06/", "local_path": "C:/Photos/Test/Dylan_2025_June", "dry_run": true, "include": ["*.jpg", "*.png", "*.mov"], "exclude": ["*.tmp", "*.log"]}}, {"id": "test-amanda-2021-jan", "type": "tool", "description": "Test: Download <PERSON>'s iPhone January 2021 photos (small subset)", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "amandas iPhone/2021/01/", "local_path": "C:/Photos/Test/Amanda_2021_Jan", "dry_run": true, "include": ["*.jpg", "*.png", "*.mov"], "exclude": ["*.tmp", "*.log"]}}], "metadata": {"priority": "high", "tags": ["s3", "test", "batch"], "created_by": "user", "notes": "Small test batch to verify batch S3 sync functionality before running the full year-by-year download."}}