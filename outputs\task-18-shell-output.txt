# Command
yt-dlp -i https://youtu.be/mNa3UxflKlk?si=2FjIX7nzQdpihwA5

# STDOUT
[youtube] Extracting URL: https://youtu.be/mNa3UxflKlk?si=2FjIX7nzQdpihwA5
[youtube] mNa3UxflKlk: Downloading webpage
[youtube] mNa3UxflKlk: Downloading tv client config
[youtube] mNa3UxflKlk: Downloading tv player API JSON
[youtube] mNa3UxflKlk: Downloading ios player API JSON
[youtube] mNa3UxflKlk: Downloading m3u8 information
[info] mNa3UxflKlk: Downloading 1 format(s): 18
[download] Don�t We Get By [mNa3UxflKlk].mp4 has already been downloaded

[download] 100% of   30.01MiB


# STDERR
WARNING: ffmpeg not found. The downloaded format may not be the best available. Installing ffmpeg is strongly recommended: https://github.com/yt-dlp/yt-dlp#dependencies


# Exit Code
0