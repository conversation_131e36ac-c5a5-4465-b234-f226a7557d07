{"time":"2025-05-25T16:25:48.402Z","event":"INFO","data":{"message":"Database initialized successfully"}}
{"time":"2025-05-25T16:25:48.402Z","event":"INFO","data":{"message":"Starting file watcher","directory":"C:\\Code\\folder-watcher\\data\\tasks"}}
{"time":"2025-05-25T16:25:48.403Z","event":"INFO","data":{"message":"File watcher started successfully"}}
{"time":"2025-05-25T16:26:24.806Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task.md"}}
{"time":"2025-05-25T16:26:24.808Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task.md"}}
{"time":"2025-05-25T16:26:24.822Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Code\\folder-watcher\\data\\tasks\\test-task.md","type":"shell","description":"Test shell command"}}
{"time":"2025-05-25T16:26:24.826Z","event":"INFO","data":{"message":"Task created successfully","taskId":1,"type":"shell"}}
{"time":"2025-05-25T16:26:24.827Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Code\\folder-watcher\\data\\tasks\\test-task.md","type":"shell","description":"Test shell command"}}
{"time":"2025-05-25T16:26:24.830Z","event":"INFO","data":{"message":"Task created successfully","taskId":2,"type":"shell"}}
{"time":"2025-05-25T17:07:15.054Z","event":"INFO","data":{"message":"Database initialized successfully"}}
{"time":"2025-05-25T17:07:15.055Z","event":"INFO","data":{"message":"Starting file watcher","directory":"C:\\Code\\folder-watcher\\data\\tasks"}}
{"time":"2025-05-25T17:07:15.055Z","event":"INFO","data":{"message":"File watcher started successfully"}}
{"time":"2025-05-25T17:07:20.057Z","event":"INFO","data":{"message":"Starting shell task","taskId":1}}
{"time":"2025-05-25T17:07:20.058Z","event":"ERROR","data":{"message":"No shell_command found in task","task":{"id":1,"parent_id":null,"description":"Test shell command","type":"shell","status":"pending","dependencies":null,"result_summary":null,"created_at":"2025-05-25 16:26:24","started_at":null,"finished_at":null}}}
{"time":"2025-05-25T17:07:20.064Z","event":"ERROR","data":{"message":"Shell task errored","taskId":1,"error":"No shell_command found in task"}}
{"time":"2025-05-25T17:07:20.069Z","event":"INFO","data":{"message":"Starting shell task","taskId":2}}
{"time":"2025-05-25T17:07:20.069Z","event":"ERROR","data":{"message":"No shell_command found in task","task":{"id":2,"parent_id":null,"description":"Test shell command","type":"shell","status":"pending","dependencies":null,"result_summary":null,"created_at":"2025-05-25 16:26:24","started_at":null,"finished_at":null}}}
{"time":"2025-05-25T17:07:20.078Z","event":"ERROR","data":{"message":"Shell task errored","taskId":2,"error":"No shell_command found in task"}}
{"time":"2025-05-25T17:15:57.657Z","event":"INFO","data":{"message":"Database initialized successfully"}}
{"time":"2025-05-25T17:15:57.657Z","event":"INFO","data":{"message":"Starting file watcher","directory":"C:\\Code\\folder-watcher\\data\\tasks"}}
{"time":"2025-05-25T17:15:57.657Z","event":"INFO","data":{"message":"File watcher started successfully"}}
{"time":"2025-05-25T17:16:32.326Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"test-task.md"}}
{"time":"2025-05-25T17:16:32.327Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Code\\folder-watcher\\data\\tasks\\test-task.md","error":"ENOENT: no such file or directory, open 'C:\\Code\\folder-watcher\\data\\tasks\\test-task.md'"}}
{"time":"2025-05-25T17:16:32.328Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Code\\folder-watcher\\data\\tasks\\test-task.md","error":"ENOENT: no such file or directory, open 'C:\\Code\\folder-watcher\\data\\tasks\\test-task.md'"}}
{"time":"2025-05-25T17:16:32.330Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-25T17:16:32.344Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Code\\folder-watcher\\data\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-25T17:16:32.348Z","event":"INFO","data":{"message":"Task created successfully","taskId":3,"type":"shell"}}
{"time":"2025-05-25T17:16:32.668Z","event":"INFO","data":{"message":"Starting shell task","taskId":3}}
{"time":"2025-05-25T17:16:32.743Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":3,"outputPath":"C:\\Code\\folder-watcher\\data\\outputs\\task-3-shell-output.txt"}}
{"time":"2025-05-25T17:16:32.748Z","event":"INFO","data":{"message":"Shell task completed","taskId":3,"outputPath":"C:\\Code\\folder-watcher\\data\\outputs\\task-3-shell-output.txt"}}
{"time":"2025-05-25T18:08:28.256Z","event":"INFO","data":{"message":"Starting file watcher","directory":"C:\\Code\\folder-watcher\\data\\tasks"}}
{"time":"2025-05-25T18:08:28.255Z","event":"INFO","data":{"message":"Database initialized successfully"}}
{"time":"2025-05-25T18:08:28.256Z","event":"INFO","data":{"message":"File watcher started successfully"}}
{"time":"2025-05-25T19:41:22.952Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-25T19:41:22.967Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Code\\folder-watcher\\data\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-25T19:41:22.971Z","event":"INFO","data":{"message":"Task created successfully","taskId":4,"type":"shell"}}
{"time":"2025-05-25T19:41:27.016Z","event":"INFO","data":{"message":"Starting shell task","taskId":4}}
{"time":"2025-05-25T19:41:27.089Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":4,"outputPath":"C:\\Code\\folder-watcher\\data\\outputs\\task-4-shell-output.txt"}}
{"time":"2025-05-25T19:41:27.094Z","event":"INFO","data":{"message":"Shell task completed","taskId":4,"outputPath":"C:\\Code\\folder-watcher\\data\\outputs\\task-4-shell-output.txt"}}
