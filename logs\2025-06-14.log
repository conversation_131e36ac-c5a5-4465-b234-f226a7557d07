{"time":"2025-06-14T15:47:31.713Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T15:47:31.730Z","level":"INFO","message":"✅ Migration 004 completed successfully"}
{"time":"2025-06-14T15:47:31.719Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T15:47:31.719Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T15:47:31.719Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T15:47:31.730Z","level":"INFO","message":"🔄 Running migration 005: Add Search Analytics"}
{"time":"2025-06-14T15:47:31.719Z","level":"INFO","message":"🔄 Running migration 004: Add Error Message Column"}
{"time":"2025-06-14T15:47:31.731Z","level":"INFO","message":"Running migration 005: Add search analytics tables"}
{"time":"2025-06-14T15:47:31.778Z","level":"INFO","message":"Migration 005 completed successfully"}
{"time":"2025-06-14T15:47:31.783Z","level":"INFO","message":"✅ Applied 2 migration(s) successfully"}
{"time":"2025-06-14T15:47:31.783Z","level":"INFO","message":"✅ Migration 005 completed successfully"}
{"time":"2025-06-14T15:49:40.129Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:49:57.679Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:49:57.680Z","level":"ERROR","message":"Failed to get search analytics via MCP","error":"MCP server meilisearch not found"}
{"time":"2025-06-14T15:50:09.406Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:50:09.406Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-14T15:50:09.407Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-14T15:50:09.407Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-14T15:50:09.408Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-14T15:50:09.408Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-14T15:50:09.409Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:50:09.409Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-14T15:50:09.412Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:50:09.413Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-14T15:50:09.413Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-14T15:50:09.415Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-14T15:50:09.429Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:50:09.446Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:50:09.451Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-14T15:50:09.451Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-14T15:50:09.449Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-14T15:50:09.451Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-14T15:50:09.447Z","level":"INFO","message":"✅ Meilisearch service initialized"}
{"time":"2025-06-14T15:50:09.461Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-14T15:50:09.463Z","level":"ERROR","message":"Failed to initialize Enhanced Task Processor","error":"Failed to connect to Chroma"}
{"time":"2025-06-14T15:50:09.467Z","level":"ERROR","message":"❌ Failed to initialize MCP Enhanced Task Processor","error":"Failed to connect to Chroma"}
{"time":"2025-06-14T15:50:09.469Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:50:09.470Z","level":"INFO","message":"Starting media file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:50:09.471Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-14T15:50:09.470Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-14T15:52:05.066Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:52:05.066Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-14T15:52:05.068Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-14T15:52:05.068Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-14T15:52:05.069Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-14T15:52:05.070Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-14T15:52:05.070Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-14T15:52:05.070Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:52:05.074Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:52:05.074Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-14T15:52:05.075Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T15:52:05.075Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T15:52:05.074Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T15:52:05.076Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T15:52:05.076Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T15:52:05.076Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T15:52:05.076Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-14T15:52:05.076Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-14T15:52:05.084Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:52:05.090Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:52:05.091Z","level":"INFO","message":"✅ Meilisearch service initialized"}
{"time":"2025-06-14T15:52:05.092Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-14T15:52:05.091Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-14T15:52:05.092Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-14T15:52:05.092Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-14T15:52:05.103Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T15:52:05.111Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-14T15:52:05.112Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\atlas/src/mcp/live-dashboard.html"}
{"time":"2025-06-14T15:52:05.113Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:52:05.114Z","level":"INFO","message":"Starting media file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:52:05.114Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-14T15:52:05.114Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-14T15:53:33.757Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:53:33.765Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:53:33.770Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:53:33.785Z","level":"INFO","message":"Media indexed in Meilisearch","mediaId":999,"documentId":"media_999","taskUid":12}
{"time":"2025-06-14T15:54:18.020Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:55:01.034Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:55:01.041Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:55:01.046Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:55:01.051Z","level":"INFO","message":"Media indexed in Meilisearch","mediaId":1,"documentId":"media_1","taskUid":17}
{"time":"2025-06-14T15:55:01.055Z","level":"INFO","message":"Media indexed in Meilisearch","mediaId":2,"documentId":"media_2","taskUid":18}
{"time":"2025-06-14T15:55:01.060Z","level":"INFO","message":"Media indexed in Meilisearch","mediaId":3,"documentId":"media_3","taskUid":19}
{"time":"2025-06-14T15:55:01.065Z","level":"INFO","message":"Media indexed in Meilisearch","mediaId":4,"documentId":"media_4","taskUid":20}
{"time":"2025-06-14T15:55:01.070Z","level":"INFO","message":"Media indexed in Meilisearch","mediaId":5,"documentId":"media_5","taskUid":21}
{"time":"2025-06-14T15:55:11.577Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:55:11.587Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:55:11.603Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:55:11.615Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T15:56:14.435Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:56:14.442Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:56:14.447Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:56:56.305Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:56:56.315Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:56:56.321Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:56:56.326Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T15:57:08.693Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:57:08.703Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:57:08.708Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:57:08.713Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T15:57:40.140Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:59:45.832Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:59:45.833Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-14T15:59:45.833Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-14T15:59:45.833Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-14T15:59:45.833Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-14T15:59:45.834Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-14T15:59:45.835Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-14T15:59:45.838Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:59:45.835Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:59:45.838Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-14T15:59:45.839Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-14T15:59:45.840Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T15:59:45.849Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:59:45.856Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:59:45.857Z","level":"INFO","message":"✅ Meilisearch service initialized"}
{"time":"2025-06-14T15:59:45.857Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-14T15:59:45.858Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-14T15:59:45.858Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-14T15:59:45.858Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-14T15:59:45.863Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T15:59:45.868Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-14T15:59:45.869Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\atlas/src/mcp/live-dashboard.html"}
{"time":"2025-06-14T15:59:45.870Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:59:45.870Z","level":"INFO","message":"Starting media file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:59:45.871Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-14T15:59:45.870Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-14T15:59:57.849Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:59:57.852Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-14T15:59:57.852Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-14T15:59:57.852Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-14T15:59:57.853Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-14T15:59:57.853Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-14T15:59:57.853Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-14T15:59:57.854Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:59:57.857Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-14T15:59:57.857Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T15:59:57.858Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T15:59:57.858Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T15:59:57.858Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T15:59:57.859Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T15:59:57.859Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T15:59:57.859Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T15:59:57.859Z","level":"INFO","message":"Running migration 006: Add transcription analytics tables"}
{"time":"2025-06-14T15:59:57.859Z","level":"INFO","message":"🔄 Running migration 006: Add Transcription Analytics"}
{"time":"2025-06-14T15:59:57.959Z","level":"INFO","message":"Migration 006 completed successfully"}
{"time":"2025-06-14T15:59:57.965Z","level":"INFO","message":"✅ Migration 006 completed successfully"}
{"time":"2025-06-14T15:59:57.965Z","level":"INFO","message":"✅ Applied 1 migration(s) successfully"}
{"time":"2025-06-14T15:59:57.965Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-14T15:59:57.971Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T15:59:57.977Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T15:59:57.978Z","level":"INFO","message":"✅ Meilisearch service initialized"}
{"time":"2025-06-14T15:59:57.978Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-14T15:59:57.979Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-14T15:59:57.979Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-14T15:59:57.979Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-14T15:59:57.984Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T15:59:57.990Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-14T15:59:57.991Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\atlas/src/mcp/live-dashboard.html"}
{"time":"2025-06-14T15:59:57.991Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T15:59:57.992Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-14T15:59:57.992Z","level":"INFO","message":"Starting media file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T15:59:57.993Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-14T16:05:51.259Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T16:05:51.260Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-14T16:06:04.632Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:07:38.209Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:08:46.243Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:08:46.244Z","level":"ERROR","message":"Failed to get model recommendation via MCP","error":"MCP server whisper not found"}
{"time":"2025-06-14T16:08:55.407Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:13:11.042Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T16:13:11.044Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-14T16:13:11.044Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-14T16:13:11.044Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-14T16:13:11.045Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-14T16:13:11.045Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-14T16:13:11.045Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-14T16:13:11.046Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T16:13:11.050Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:13:11.050Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-14T16:13:11.052Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T16:13:11.052Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T16:13:11.051Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T16:13:11.052Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T16:13:11.052Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T16:13:11.053Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-14T16:13:11.052Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T16:13:11.053Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-14T16:13:11.053Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-14T16:13:11.060Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T16:13:11.065Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T16:13:11.066Z","level":"INFO","message":"✅ Meilisearch service initialized"}
{"time":"2025-06-14T16:13:11.066Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-14T16:13:11.067Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-14T16:13:11.067Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-14T16:13:11.067Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-14T16:13:11.074Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T16:13:11.080Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-14T16:13:11.081Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\atlas/src/mcp/live-dashboard.html"}
{"time":"2025-06-14T16:13:11.082Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T16:13:11.082Z","level":"INFO","message":"Starting media file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T16:13:11.083Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-14T16:13:11.082Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-14T16:13:22.195Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T16:13:22.197Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/error"}
{"time":"2025-06-14T16:13:22.197Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/archive"}
{"time":"2025-06-14T16:13:22.197Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/processing"}
{"time":"2025-06-14T16:13:22.198Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/outputs"}
{"time":"2025-06-14T16:13:22.198Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/logs"}
{"time":"2025-06-14T16:13:22.199Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/dashboard"}
{"time":"2025-06-14T16:13:22.199Z","level":"INFO","message":"Ensured folder exists","folder":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T16:13:22.203Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:13:22.203Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-14T16:13:22.204Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T16:13:22.205Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T16:13:22.204Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T16:13:22.205Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T16:13:22.203Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T16:13:22.205Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T16:13:22.205Z","level":"INFO","message":"Running migration 007: Add media intelligence tables"}
{"time":"2025-06-14T16:13:22.205Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-14T16:13:22.205Z","level":"INFO","message":"🔄 Running migration 007: Add Media Intelligence"}
{"time":"2025-06-14T16:13:22.335Z","level":"INFO","message":"Migration 007 completed successfully"}
{"time":"2025-06-14T16:13:22.340Z","level":"INFO","message":"✅ Migration 007 completed successfully"}
{"time":"2025-06-14T16:13:22.341Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-14T16:13:22.340Z","level":"INFO","message":"✅ Applied 1 migration(s) successfully"}
{"time":"2025-06-14T16:13:22.347Z","level":"INFO","message":"Meilisearch index created or already exists","indexName":"media_index"}
{"time":"2025-06-14T16:13:22.352Z","level":"INFO","message":"Meilisearch index configured successfully"}
{"time":"2025-06-14T16:13:22.353Z","level":"INFO","message":"✅ Meilisearch service initialized"}
{"time":"2025-06-14T16:13:22.354Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-14T16:13:22.353Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-14T16:13:22.354Z","level":"INFO","message":"🚀 Initializing MCP Enhanced Task Processor..."}
{"time":"2025-06-14T16:13:22.354Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-14T16:13:22.359Z","level":"INFO","message":"Embedding manager initialized"}
{"time":"2025-06-14T16:13:22.363Z","level":"INFO","message":"Enhanced Task Processor initialized"}
{"time":"2025-06-14T16:13:22.365Z","level":"INFO","message":"✅ MCP Enhanced Task Processor initialized","websocketUrl":"ws://localhost:8080","dashboardPath":"C:\\Code\\atlas/src/mcp/live-dashboard.html"}
{"time":"2025-06-14T16:13:22.365Z","level":"INFO","message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/incoming"}
{"time":"2025-06-14T16:13:22.366Z","level":"INFO","message":"Starting media file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/media"}
{"time":"2025-06-14T16:13:22.366Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-14T16:13:22.367Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-14T16:17:47.919Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 007 (Add Media Intelligence) already applied"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-14T16:17:47.921Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-14T16:18:24.998Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:20:18.088Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:22:43.543Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:22:43.544Z","level":"ERROR","message":"Failed to analyze content discovery via MCP","error":"MCP server media_intelligence not found"}
{"time":"2025-06-14T16:22:53.662Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:27.990Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:27.997Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:28.000Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:28.012Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:32.985Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:32.992Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:32.994Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:38:33.007Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:04.015Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:04.019Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:04.022Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:04.033Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:09.028Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:09.039Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:09.040Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:09.055Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:40.056Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:40.070Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:40.070Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:39:40.073Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:40:11.120Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:40:11.157Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:40:11.234Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:40:11.343Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-14T16:51:11.925Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-14T16:51:11.956Z","level":"ERROR","message":"Shell task failed","taskId":2,"exitCode":1,"stderr":""}
{"time":"2025-06-14T16:51:11.958Z","level":"ERROR","message":"No shell_command found in task","task":{"id":3,"type":"shell","shell_command":"","status":"pending","result":null,"description":"Test missing command"}}
{"time":"2025-06-14T16:51:11.990Z","level":"ERROR","message":"Shell task failed","taskId":4,"exitCode":1,"stderr":""}
{"time":"2025-06-14T16:51:11.993Z","level":"INFO","message":"LLM task executed successfully","taskId":1,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-1-llm-output.txt"}
{"time":"2025-06-14T16:51:12.001Z","level":"ERROR","message":"Ollama API error: 500 Internal Server Error","taskId":2}
{"time":"2025-06-14T16:51:12.003Z","level":"ERROR","message":"Error executing LLM task","taskId":3,"error":"Network error"}
{"time":"2025-06-14T16:51:12.005Z","level":"INFO","message":"Code task executed successfully","taskId":1,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-1-generated-code.py","lang":"python"}
{"time":"2025-06-14T16:51:12.016Z","level":"INFO","message":"Code task executed successfully","taskId":2,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-2-generated-code.js","lang":"javascript"}
{"time":"2025-06-14T16:51:12.050Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-14T16:51:12.054Z","level":"INFO","message":"LLM task executed successfully","taskId":2,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-2-llm-output.txt"}
{"time":"2025-06-14T16:51:12.085Z","level":"ERROR","message":"Shell task failed","taskId":1,"exitCode":1,"stderr":""}
{"time":"2025-06-14T16:51:21.814Z","level":"INFO","message":"Task parsed and validated successfully","filePath":"\\tmp\\folder-watcher-test\\task.yaml","taskType":"shell","taskId":"task-1749919881814"}
{"time":"2025-06-14T16:51:21.832Z","level":"INFO","message":"Task parsed and validated successfully","filePath":"\\tmp\\folder-watcher-test\\task.md","taskType":"code","taskId":"task-1749919881832"}
{"time":"2025-06-14T16:51:21.841Z","level":"INFO","message":"Task parsed and validated successfully","filePath":"\\tmp\\folder-watcher-test\\tool-task.yaml","taskType":"tool","taskId":"task-1749919881841"}
{"time":"2025-06-14T16:51:21.845Z","level":"INFO","message":"Task parsed and validated successfully","filePath":"\\tmp\\folder-watcher-test\\batch-task.yaml","taskType":"batch","taskId":"task-1749919881845"}
{"time":"2025-06-14T16:51:21.860Z","level":"INFO","message":"Task parsed and validated successfully","filePath":"\\tmp\\folder-watcher-test\\dependent-task.yaml","taskType":"review","taskId":"task-1749919881860"}
