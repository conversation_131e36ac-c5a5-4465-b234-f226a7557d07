# Command
pip3 install --upgrade yt-dlp

# STDOUT
Requirement already satisfied: yt-dlp in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (2024.12.23)
Collecting yt-dlp
  Downloading yt_dlp-2025.5.22-py3-none-any.whl.metadata (174 kB)
Downloading yt_dlp-2025.5.22-py3-none-any.whl (3.3 MB)
   ---------------------------------------- 3.3/3.3 MB 9.6 MB/s eta 0:00:00
Installing collected packages: yt-dlp
  Attempting uninstall: yt-dlp
    Found existing installation: yt-dlp 2024.12.23
    Uninstalling yt-dlp-2024.12.23:
      Successfully uninstalled yt-dlp-2024.12.23
Successfully installed yt-dlp-2025.5.22


# STDERR
WARNING: Ignoring invalid distribution -t-dlp (c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages)

[notice] A new release of pip is available: 25.0.1 -> 25.1.1
[notice] To update, run: python.exe -m pip install --upgrade pip


# Exit Code
0