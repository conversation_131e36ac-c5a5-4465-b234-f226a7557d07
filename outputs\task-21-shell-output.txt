# Command
#!/bin/bash

# Script to download and install FFmpeg binary on Windows

# Define variables
FFMPEG_URL="https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
DOWNLOAD_DIR="$HOME/ffmpeg"
INSTALL_DIR="$HOME/ffmpeg/bin"
ZIP_FILE="$HOME/ffmpeg-release-essentials.zip"

# Create download directory
mkdir -p "$DOWNLOAD_DIR"

# Download FFmpeg binary
echo "Downloading FFmpeg binary..."
curl -L "$FFMPEG_URL" -o "$ZIP_FILE"

# Check if download was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to download FFmpeg. Check your internet connection."
  exit 1
fi

# Unzip the downloaded file
echo "Extracting FFmpeg..."
unzip -o "$ZIP_FILE" -d "$DOWNLOAD_DIR"

# Check if unzip was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to unzip FFmpeg. Ensure 'unzip' is installed."
  exit 1
fi

# Find the extracted FFmpeg folder (name may vary, e.g., ffmpeg-7.0-essentials_build)
FFMPEG_EXTRACTED=$(find "$DOWNLOAD_DIR" -maxdepth 1 -type d -name "ffmpeg-*" | head -n 1)
if [ -z "$FFMPEG_EXTRACTED" ]; then
  echo "Error: Could not find extracted FFmpeg folder."
  exit 1
fi

# Move the bin directory to the desired install location
mv "$FFMPEG_EXTRACTED/bin" "$INSTALL_DIR"
if [ $? -ne 0 ]; then
  echo "Error: Failed to move FFmpeg binaries."
  exit 1
fi

# Clean up
rm "$ZIP_FILE"
rm -rf "$FFMPEG_EXTRACTED"

# Add FFmpeg to PATH in .bashrc or .bash_profile
BASH_PROFILE="$HOME/.bashrc"
if [ ! -f "$BASH_PROFILE" ]; then
  BASH_PROFILE="$HOME/.bash_profile"
fi

echo "Adding FFmpeg to PATH..."
if ! grep -q "$INSTALL_DIR" "$BASH_PROFILE"; then
  echo "export PATH=\"\$PATH:$INSTALL_DIR\"" >> "$BASH_PROFILE"
fi

# Reload the shell configuration
source "$BASH_PROFILE"

# Verify installation
echo "Verifying FFmpeg installation..."
ffmpeg -version > /dev/null 2>&1
if [ $? -eq 0 ]; then
  echo "FFmpeg installed successfully! Version:"
  ffmpeg -version
else
  echo "Error: FFmpeg installation failed. Check if the binary is accessible."
  exit 1
fi

echo "FFmpeg is installed in $INSTALL_DIR and added to your PATH."
echo "You can now use 'ffmpeg' from any Bash terminal."

# STDOUT
W i n d o w s   S u b s y s t e m   f o r   L i n u x   h a s   n o   i n s t a l l e d   d i s t r i b u t i o n s . 
 
 Y o u   c a n   r e s o l v e   t h i s   b y   i n s t a l l i n g   a   d i s t r i b u t i o n   w i t h   t h e   i n s t r u c t i o n s   b e l o w : 
 
 
 
 U s e   ' w s l . e x e   - - l i s t   - - o n l i n e '   t o   l i s t   a v a i l a b l e   d i s t r i b u t i o n s 
 
 a n d   ' w s l . e x e   - - i n s t a l l   < D i s t r o > '   t o   i n s t a l l . 
 
 

# STDERR


# Exit Code
1