# Command
#!/bin/bash

# Script to download and install FFmpeg binary on Windows

# Define variables
FFMPEG_URL="https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
DOWNLOAD_DIR="$HOME/ffmpeg"
INSTALL_DIR="$HOME/ffmpeg/bin"
ZIP_FILE="$HOME/ffmpeg-release-essentials.zip"

# Create download directory
mkdir -p "$DOWNLOAD_DIR"

# Download FFmpeg binary
echo "Downloading FFmpeg binary..."
curl -L "$FFMPEG_URL" -o "$ZIP_FILE"

# Check if download was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to download FFmpeg. Check your internet connection."
  exit 1
fi

# Unzip the downloaded file
echo "Extracting FFmpeg..."
unzip -o "$ZIP_FILE" -d "$DOWNLOAD_DIR"

# Check if unzip was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to unzip FFmpeg. Ensure 'unzip' is installed."
  exit 1
fi

# Find the extracted FFmpeg folder (name may vary, e.g., ffmpeg-7.0-essentials_build)
FFMPEG_EXTRACTED=$(find "$DOWNLOAD_DIR" -maxdepth 1 -type d -name "ffmpeg-*" | head -n 1)
if [ -z "$FFMPEG_EXTRACTED" ]; then
  echo "Error: Could not find extracted FFmpeg folder."
  exit 1
fi

# Move the bin directory to the desired install location
mv "$FFMPEG_EXTRACTED/bin" "$INSTALL_DIR"
if [ $? -ne 0 ]; then
  echo "Error: Failed to move FFmpeg binaries."
  exit 1
fi

# Clean up
rm "$ZIP_FILE"
rm -rf "$FFMPEG_EXTRACTED"

# Add FFmpeg to PATH in .bashrc or .bash_profile
BASH_PROFILE="$HOME/.bashrc"
if [ ! -f "$BASH_PROFILE" ]; then
  BASH_PROFILE="$HOME/.bash_profile"
fi

echo "Adding FFmpeg to PATH..."
if ! grep -q "$INSTALL_DIR" "$BASH_PROFILE"; then
  echo "export PATH=\"\$PATH:$INSTALL_DIR\"" >> "$BASH_PROFILE"
fi

# Reload the shell configuration
source "$BASH_PROFILE"

# Verify installation
echo "Verifying FFmpeg installation..."
ffmpeg -version > /dev/null 2>&1
if [ $? -eq 0 ]; then
  echo "FFmpeg installed successfully! Version:"
  ffmpeg -version
else
  echo "Error: FFmpeg installation failed. Check if the binary is accessible."
  exit 1
fi

echo "FFmpeg is installed in $INSTALL_DIR and added to your PATH."
echo "You can now use 'ffmpeg' from any Bash terminal."

# STDOUT
Downloading FFmpeg binary...
Extracting FFmpeg...
Archive:  /c/Users/<USER>/ffmpeg-release-essentials.zip
   creating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/
   creating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/bin/
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/bin/ffmpeg.exe  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/bin/ffplay.exe  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/bin/ffprobe.exe  
   creating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/bootstrap.min.css  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/community.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/default.css  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/developer.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/faq.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/fate.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-all.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-bitstream-filters.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-codecs.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-devices.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-filters.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-formats.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-protocols.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-resampler.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-scaler.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg-utils.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffmpeg.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffplay-all.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffplay.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffprobe-all.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/ffprobe.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/general.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/git-howto.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libavcodec.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libavdevice.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libavfilter.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libavformat.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libavutil.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libswresample.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/libswscale.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/mailing-list-faq.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/nut.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/platform.html  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/doc/style.min.css  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/LICENSE  
   creating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/presets/
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/presets/libvpx-1080p.ffpreset  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/presets/libvpx-1080p50_60.ffpreset  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/presets/libvpx-360p.ffpreset  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/presets/libvpx-720p.ffpreset  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/presets/libvpx-720p50_60.ffpreset  
  inflating: /c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/README.txt  
Adding FFmpeg to PATH...
Verifying FFmpeg installation...
FFmpeg installed successfully! Version:
ffmpeg version 7.1.1-essentials_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
libavutil      59. 39.100 / 59. 39.100
libavcodec     61. 19.101 / 61. 19.101
libavformat    61.  7.100 / 61.  7.100
libavdevice    61.  3.100 / 61.  3.100
libavfilter    10.  4.100 / 10.  4.100
libswscale      8.  3.100 /  8.  3.100
libswresample   5.  3.100 /  5.  3.100
libpostproc    58.  3.100 / 58.  3.100
FFmpeg is installed in /c/Users/<USER>/ffmpeg/bin and added to your PATH.
You can now use 'ffmpeg' from any Bash terminal.


# STDERR
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   284  100   284    0     0    989      0 --:--:-- --:--:-- --:--:--   993

  0 87.9M    0  207k    0     0   408k      0  0:03:40 --:--:--  0:03:40  408k
  0 87.9M    0  543k    0     0   357k      0  0:04:12  0:00:01  0:04:11  331k
  5 87.9M    5 5277k    0     0  2103k      0  0:00:42  0:00:02  0:00:40 2534k
 36 87.9M   36 31.7M    0     0  9270k      0  0:00:09  0:00:03  0:00:06 10.5M
 40 87.9M   40 35.6M    0     0  8098k      0  0:00:11  0:00:04  0:00:07 9077k
 44 87.9M   44 39.2M    0     0  7294k      0  0:00:12  0:00:05  0:00:07 7995k
 48 87.9M   48 43.0M    0     0  6774k      0  0:00:13  0:00:06  0:00:07 8733k
 63 87.9M   63 55.6M    0     0  7152k      0  0:00:12  0:00:07  0:00:05 9472k
 79 87.9M   79 69.5M    0     0  8350k      0  0:00:10  0:00:08  0:00:02 7710k
 79 87.9M   79 69.6M    0     0  7491k      0  0:00:12  0:00:09  0:00:03 6945k
 79 87.9M   79 69.7M    0     0  6793k      0  0:00:13  0:00:10  0:00:03 6239k
 79 87.9M   79 69.7M    0     0  6206k      0  0:00:14  0:00:11  0:00:03 5468k
 79 87.9M   79 69.8M    0     0  5718k      0  0:00:15  0:00:12  0:00:03 3199k
 83 87.9M   83 73.3M    0     0  5561k      0  0:00:16  0:00:13  0:00:03  780k
 85 87.9M   85 75.2M    0     0  5301k      0  0:00:16  0:00:14  0:00:02 1145k
 85 87.9M   85 75.3M    0     0  4979k      0  0:00:18  0:00:15  0:00:03 1164k
 86 87.9M   86 75.9M    0     0  4700k      0  0:00:19  0:00:16  0:00:03 1253k
 89 87.9M   89 79.1M    0     0  4627k      0  0:00:19  0:00:17  0:00:02 1897k
 99 87.9M   99 87.9M    0     0  4865k      0  0:00:18  0:00:18 --:--:-- 2984k
100 87.9M  100 87.9M    0     0  4867k      0  0:00:18  0:00:18 --:--:-- 3281k


# Exit Code
0