# Command
pip3 install --upgrade yt-dlp

# STDOUT
Requirement already satisfied: yt-dlp in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (2025.5.22)


# STDERR
WARNING: Ignoring invalid distribution -t-dlp (c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages)
WARNING: Ignoring invalid distribution -t-dlp (c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages)
WARNING: Ignoring invalid distribution -t-dlp (c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages)

[notice] A new release of pip is available: 25.0.1 -> 25.1.1
[notice] To update, run: python.exe -m pip install --upgrade pip


# Exit Code
0