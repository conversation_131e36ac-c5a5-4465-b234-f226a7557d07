{"time":"2025-05-27T01:46:58.426Z","event":"INFO","data":{"message":"Database initialized successfully"}}
{"time":"2025-05-27T01:46:58.428Z","event":"INFO","data":{"message":"Starting file watcher","directory":"C:/Users/<USER>/Sync/AI Drop Box/tasks"}}
{"time":"2025-05-27T01:46:58.428Z","event":"INFO","data":{"message":"File watcher started successfully"}}
{"time":"2025-05-27T10:18:33.255Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-27T10:18:33.277Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-27T10:18:33.283Z","event":"INFO","data":{"message":"Task created successfully","taskId":5,"type":"shell"}}
{"time":"2025-05-27T10:18:35.769Z","event":"INFO","data":{"message":"Starting shell task","taskId":5}}
{"time":"2025-05-27T10:18:35.983Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":5,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-5-shell-output.txt"}}
{"time":"2025-05-27T10:18:35.989Z","event":"INFO","data":{"message":"Shell task completed","taskId":5,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-5-shell-output.txt"}}
{"time":"2025-05-27T13:06:11.927Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-27T13:06:11.928Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-27T13:06:11.932Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-27T13:06:11.936Z","event":"INFO","data":{"message":"Task created successfully","taskId":6,"type":"shell"}}
{"time":"2025-05-27T13:06:11.938Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-27T13:06:11.942Z","event":"INFO","data":{"message":"Task created successfully","taskId":7,"type":"shell"}}
{"time":"2025-05-27T13:06:12.090Z","event":"INFO","data":{"message":"Starting shell task","taskId":6}}
{"time":"2025-05-27T13:06:12.214Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":6,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-6-shell-output.txt"}}
{"time":"2025-05-27T13:06:12.219Z","event":"INFO","data":{"message":"Shell task completed","taskId":6,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-6-shell-output.txt"}}
{"time":"2025-05-27T13:06:12.222Z","event":"INFO","data":{"message":"Starting shell task","taskId":7}}
{"time":"2025-05-27T13:06:12.280Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":7,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-7-shell-output.txt"}}
{"time":"2025-05-27T13:06:12.284Z","event":"INFO","data":{"message":"Shell task completed","taskId":7,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-7-shell-output.txt"}}
{"time":"2025-05-27T14:07:20.147Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:07:20.147Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:07:20.150Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:07:20.150Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:07:20.150Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:07:20.150Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:07:20.151Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:07:20.155Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:07:20.154Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:07:20.337Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:07:20.338Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:07:20.338Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp'"}}
{"time":"2025-05-27T14:07:20.339Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp'"}}
{"time":"2025-05-27T14:07:20.343Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"task 2025-05-27.md"}}
{"time":"2025-05-27T14:07:20.348Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","type":"shell","description":"Test shell command","shell_command":"yt-dlp -h"}}
{"time":"2025-05-27T14:07:20.352Z","event":"INFO","data":{"message":"Task created successfully","taskId":8,"type":"shell"}}
{"time":"2025-05-27T14:07:20.353Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","type":"shell","description":"Test shell command","shell_command":"yt-dlp -h"}}
{"time":"2025-05-27T14:07:20.357Z","event":"INFO","data":{"message":"Task created successfully","taskId":9,"type":"shell"}}
{"time":"2025-05-27T14:07:23.126Z","event":"INFO","data":{"message":"Starting shell task","taskId":8}}
{"time":"2025-05-27T14:07:24.011Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":8,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-8-shell-output.txt"}}
{"time":"2025-05-27T14:07:24.016Z","event":"INFO","data":{"message":"Shell task completed","taskId":8,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-8-shell-output.txt"}}
{"time":"2025-05-27T14:07:24.019Z","event":"INFO","data":{"message":"Starting shell task","taskId":9}}
{"time":"2025-05-27T14:07:24.378Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":9,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-9-shell-output.txt"}}
{"time":"2025-05-27T14:07:24.383Z","event":"INFO","data":{"message":"Shell task completed","taskId":9,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-9-shell-output.txt"}}
{"time":"2025-05-27T14:22:34.776Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-27T14:22:34.780Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-27T14:22:34.788Z","event":"INFO","data":{"message":"Task created successfully","taskId":10,"type":"shell"}}
{"time":"2025-05-27T14:22:34.791Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:22:34.792Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:22:34.793Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:22:34.793Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:22:34.793Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:22:34.793Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:22:35.282Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:22:35.283Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:22:35.283Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"task 2025-05-27.md"}}
{"time":"2025-05-27T14:22:35.285Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md'"}}
{"time":"2025-05-27T14:22:35.285Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md'"}}
{"time":"2025-05-27T14:22:35.288Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:22:35.289Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"task 2025-05-27.md"}}
{"time":"2025-05-27T14:22:35.289Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp'"}}
{"time":"2025-05-27T14:22:35.289Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp'"}}
{"time":"2025-05-27T14:22:35.294Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","type":"shell","description":"Test shell command","shell_command":"yt-dlp -U"}}
{"time":"2025-05-27T14:22:35.299Z","event":"INFO","data":{"message":"Task created successfully","taskId":11,"type":"shell"}}
{"time":"2025-05-27T14:22:35.300Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","type":"shell","description":"Test shell command","shell_command":"yt-dlp -U"}}
{"time":"2025-05-27T14:22:35.305Z","event":"INFO","data":{"message":"Task created successfully","taskId":12,"type":"shell"}}
{"time":"2025-05-27T14:22:35.306Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","type":"shell","description":"Test shell command","shell_command":"yt-dlp -U"}}
{"time":"2025-05-27T14:22:35.310Z","event":"INFO","data":{"message":"Task created successfully","taskId":13,"type":"shell"}}
{"time":"2025-05-27T14:22:39.753Z","event":"INFO","data":{"message":"Starting shell task","taskId":10}}
{"time":"2025-05-27T14:22:39.813Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":10,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-10-shell-output.txt"}}
{"time":"2025-05-27T14:22:39.817Z","event":"INFO","data":{"message":"Shell task completed","taskId":10,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-10-shell-output.txt"}}
{"time":"2025-05-27T14:22:39.821Z","event":"INFO","data":{"message":"Starting shell task","taskId":11}}
{"time":"2025-05-27T14:22:40.774Z","event":"ERROR","data":{"message":"Shell task failed","taskId":11,"exitCode":100,"stderr":"ERROR: You installed yt-dlp with pip or using the wheel from PyPi; Use that to update\n"}}
{"time":"2025-05-27T14:22:40.779Z","event":"ERROR","data":{"message":"Shell task errored","taskId":11,"error":"ERROR: You installed yt-dlp with pip or using the wheel from PyPi; Use that to update\n"}}
{"time":"2025-05-27T14:22:40.783Z","event":"INFO","data":{"message":"Starting shell task","taskId":12}}
{"time":"2025-05-27T14:22:41.704Z","event":"ERROR","data":{"message":"Shell task failed","taskId":12,"exitCode":100,"stderr":"ERROR: You installed yt-dlp with pip or using the wheel from PyPi; Use that to update\n"}}
{"time":"2025-05-27T14:22:41.715Z","event":"ERROR","data":{"message":"Shell task errored","taskId":12,"error":"ERROR: You installed yt-dlp with pip or using the wheel from PyPi; Use that to update\n"}}
{"time":"2025-05-27T14:22:41.718Z","event":"INFO","data":{"message":"Starting shell task","taskId":13}}
{"time":"2025-05-27T14:22:42.626Z","event":"ERROR","data":{"message":"Shell task failed","taskId":13,"exitCode":100,"stderr":"ERROR: You installed yt-dlp with pip or using the wheel from PyPi; Use that to update\n"}}
{"time":"2025-05-27T14:22:42.637Z","event":"ERROR","data":{"message":"Shell task errored","taskId":13,"error":"ERROR: You installed yt-dlp with pip or using the wheel from PyPi; Use that to update\n"}}
{"time":"2025-05-27T14:33:37.220Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:33:37.221Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:33:37.222Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:33:37.223Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:33:37.223Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:33:37.224Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:33:37.226Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:33:37.227Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:33:37.227Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T14:33:37.390Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:33:37.391Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:33:37.391Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"task 2025-05-27.md"}}
{"time":"2025-05-27T14:33:37.392Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md'"}}
{"time":"2025-05-27T14:33:37.392Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md'"}}
{"time":"2025-05-27T14:33:37.395Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task 2025-05-27.md.tmp"}}
{"time":"2025-05-27T14:33:37.395Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"task 2025-05-27.md"}}
{"time":"2025-05-27T14:33:37.396Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp'"}}
{"time":"2025-05-27T14:33:37.396Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp'"}}
{"time":"2025-05-27T14:33:37.402Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","type":"shell","description":"Test shell command","shell_command":"pip3 install --upgrade yt-dlp"}}
{"time":"2025-05-27T14:33:37.410Z","event":"INFO","data":{"message":"Task created successfully","taskId":14,"type":"shell"}}
{"time":"2025-05-27T14:33:37.411Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task 2025-05-27.md.tmp","type":"shell","description":"Test shell command","shell_command":"pip3 install --upgrade yt-dlp"}}
{"time":"2025-05-27T14:33:37.425Z","event":"INFO","data":{"message":"Task created successfully","taskId":15,"type":"shell"}}
{"time":"2025-05-27T14:33:37.425Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","type":"shell","description":"Test shell command","shell_command":"pip3 install --upgrade yt-dlp"}}
{"time":"2025-05-27T14:33:37.429Z","event":"INFO","data":{"message":"Task created successfully","taskId":16,"type":"shell"}}
{"time":"2025-05-27T14:33:40.945Z","event":"INFO","data":{"message":"Starting shell task","taskId":14}}
{"time":"2025-05-27T14:33:45.941Z","event":"INFO","data":{"message":"Starting shell task","taskId":15}}
{"time":"2025-05-27T14:33:49.049Z","event":"ERROR","data":{"message":"Shell task failed","taskId":15,"exitCode":1,"stderr":"ERROR: Could not install packages due to an OSError: [Errno 2] No such file or directory: 'c:\\\\users\\\\<USER>\\\\appdata\\\\local\\\\programs\\\\python\\\\python310\\\\share\\\\man\\\\man1\\\\yt-dlp.1'\r\n\r\n\r\n[notice] A new release of pip is available: 25.0.1 -> 25.1.1\r\n[notice] To update, run: python.exe -m pip install --upgrade pip\r\n"}}
{"time":"2025-05-27T14:33:49.056Z","event":"ERROR","data":{"message":"Shell task errored","taskId":15,"error":"ERROR: Could not install packages due to an OSError: [Errno 2] No such file or directory: 'c:\\\\users\\\\<USER>\\\\appdata\\\\local\\\\programs\\\\python\\\\python310\\\\share\\\\man\\\\man1\\\\yt-dlp.1'\r\n\r\n\r\n[notice] A new release of pip is available: 25.0.1 -> 25.1.1\r\n[notice] To update, run: python.exe -m pip install --upgrade pip\r\n"}}
{"time":"2025-05-27T14:33:49.060Z","event":"INFO","data":{"message":"Starting shell task","taskId":16}}
{"time":"2025-05-27T14:33:50.944Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":16,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-16-shell-output.txt"}}
{"time":"2025-05-27T14:33:50.951Z","event":"INFO","data":{"message":"Shell task completed","taskId":16,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-16-shell-output.txt"}}
{"time":"2025-05-27T14:34:01.306Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":14,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-14-shell-output.txt"}}
{"time":"2025-05-27T14:34:01.313Z","event":"INFO","data":{"message":"Shell task completed","taskId":14,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-14-shell-output.txt"}}
{"time":"2025-05-27T14:34:01.317Z","event":"INFO","data":{"message":"Starting shell task","taskId":15}}
{"time":"2025-05-27T14:34:03.068Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":15,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-15-shell-output.txt"}}
{"time":"2025-05-27T14:34:03.073Z","event":"INFO","data":{"message":"Shell task completed","taskId":15,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-15-shell-output.txt"}}
{"time":"2025-05-27T14:34:03.077Z","event":"INFO","data":{"message":"Starting shell task","taskId":16}}
{"time":"2025-05-27T14:34:04.840Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":16,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-16-shell-output.txt"}}
{"time":"2025-05-27T14:34:04.845Z","event":"INFO","data":{"message":"Shell task completed","taskId":16,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-16-shell-output.txt"}}
{"time":"2025-05-27T15:25:37.789Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task yt-dlp.md.tmp"}}
{"time":"2025-05-27T15:25:37.789Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task yt-dlp.md.tmp"}}
{"time":"2025-05-27T15:25:37.790Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T15:25:37.791Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T15:25:37.793Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T15:25:37.793Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T15:25:38.264Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task yt-dlp.md.tmp"}}
{"time":"2025-05-27T15:25:38.265Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~task yt-dlp.md.tmp"}}
{"time":"2025-05-27T15:25:38.269Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~task yt-dlp.md.tmp"}}
{"time":"2025-05-27T15:25:38.269Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp'"}}
{"time":"2025-05-27T15:25:38.269Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp'"}}
{"time":"2025-05-27T15:25:38.270Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp'"}}
{"time":"2025-05-27T15:25:38.270Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp'"}}
{"time":"2025-05-27T15:25:38.275Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"task yt-dlp.md"}}
{"time":"2025-05-27T15:25:38.275Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"task 2025-05-27.md"}}
{"time":"2025-05-27T15:25:38.276Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md'"}}
{"time":"2025-05-27T15:25:38.276Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task 2025-05-27.md'"}}
{"time":"2025-05-27T15:25:38.281Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\task yt-dlp.md","type":"shell","description":"Test shell command","shell_command":"yt-dlp -i https://youtu.be/mNa3UxflKlk?si=2FjIX7nzQdpihwA5"}}
{"time":"2025-05-27T15:25:38.286Z","event":"INFO","data":{"message":"Task created successfully","taskId":17,"type":"shell"}}
{"time":"2025-05-27T15:25:38.287Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~task yt-dlp.md.tmp","type":"shell","description":"Test shell command","shell_command":"yt-dlp -i https://youtu.be/mNa3UxflKlk?si=2FjIX7nzQdpihwA5"}}
{"time":"2025-05-27T15:25:38.292Z","event":"INFO","data":{"message":"Task created successfully","taskId":18,"type":"shell"}}
{"time":"2025-05-27T15:25:41.434Z","event":"INFO","data":{"message":"Starting shell task","taskId":17}}
{"time":"2025-05-27T15:25:46.431Z","event":"INFO","data":{"message":"Starting shell task","taskId":18}}
{"time":"2025-05-27T15:25:47.978Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":17,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-17-shell-output.txt"}}
{"time":"2025-05-27T15:25:47.989Z","event":"INFO","data":{"message":"Shell task completed","taskId":17,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-17-shell-output.txt"}}
{"time":"2025-05-27T15:25:47.993Z","event":"INFO","data":{"message":"Starting shell task","taskId":18}}
{"time":"2025-05-27T15:25:50.675Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":18,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-18-shell-output.txt"}}
{"time":"2025-05-27T15:25:50.687Z","event":"INFO","data":{"message":"Shell task completed","taskId":18,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-18-shell-output.txt"}}
{"time":"2025-05-27T15:25:51.813Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":18,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-18-shell-output.txt"}}
{"time":"2025-05-27T15:25:51.817Z","event":"INFO","data":{"message":"Shell task completed","taskId":18,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-18-shell-output.txt"}}
{"time":"2025-05-27T16:06:13.926Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2.md"}}
{"time":"2025-05-27T16:06:13.929Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~test-task2 2.md.tmp"}}
{"time":"2025-05-27T16:06:13.931Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\test-task2.md","type":"shell","description":"Test shell command","shell_command":"ollama list"}}
{"time":"2025-05-27T16:06:13.935Z","event":"INFO","data":{"message":"Task created successfully","taskId":19,"type":"shell"}}
{"time":"2025-05-27T16:06:13.937Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T16:06:13.937Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T16:06:13.939Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~test-task2 2.md.tmp"}}
{"time":"2025-05-27T16:06:13.939Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T16:06:13.939Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","error":"Invalid task file format. Must contain YAML frontmatter between --- markers"}}
{"time":"2025-05-27T16:06:14.132Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"~syncthing~test-task2 2.md.tmp"}}
{"time":"2025-05-27T16:06:14.134Z","event":"INFO","data":{"message":"File event detected","eventType":"rename","filename":"~syncthing~test-task2 2.md.tmp"}}
{"time":"2025-05-27T16:06:14.134Z","event":"ERROR","data":{"message":"Failed to parse task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp'"}}
{"time":"2025-05-27T16:06:14.134Z","event":"ERROR","data":{"message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp'"}}
{"time":"2025-05-27T16:06:14.136Z","event":"INFO","data":{"message":"File event detected","eventType":"change","filename":"test-task2 2.md"}}
{"time":"2025-05-27T16:06:14.144Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\test-task2 2.md","type":"shell","description":"Test shell command","shell_command":"#!/bin/bash\n\n# Script to download and install FFmpeg binary on Windows\n\n# Define variables\nFFMPEG_URL=\"https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip\"\nDOWNLOAD_DIR=\"$HOME/ffmpeg\"\nINSTALL_DIR=\"$HOME/ffmpeg/bin\"\nZIP_FILE=\"$HOME/ffmpeg-release-essentials.zip\"\n\n# Create download directory\nmkdir -p \"$DOWNLOAD_DIR\"\n\n# Download FFmpeg binary\necho \"Downloading FFmpeg binary...\"\ncurl -L \"$FFMPEG_URL\" -o \"$ZIP_FILE\"\n\n# Check if download was successful\nif [ $? -ne 0 ]; then\n  echo \"Error: Failed to download FFmpeg. Check your internet connection.\"\n  exit 1\nfi\n\n# Unzip the downloaded file\necho \"Extracting FFmpeg...\"\nunzip -o \"$ZIP_FILE\" -d \"$DOWNLOAD_DIR\"\n\n# Check if unzip was successful\nif [ $? -ne 0 ]; then\n  echo \"Error: Failed to unzip FFmpeg. Ensure 'unzip' is installed.\"\n  exit 1\nfi\n\n# Find the extracted FFmpeg folder (name may vary, e.g., ffmpeg-7.0-essentials_build)\nFFMPEG_EXTRACTED=$(find \"$DOWNLOAD_DIR\" -maxdepth 1 -type d -name \"ffmpeg-*\" | head -n 1)\nif [ -z \"$FFMPEG_EXTRACTED\" ]; then\n  echo \"Error: Could not find extracted FFmpeg folder.\"\n  exit 1\nfi\n\n# Move the bin directory to the desired install location\nmv \"$FFMPEG_EXTRACTED/bin\" \"$INSTALL_DIR\"\nif [ $? -ne 0 ]; then\n  echo \"Error: Failed to move FFmpeg binaries.\"\n  exit 1\nfi\n\n# Clean up\nrm \"$ZIP_FILE\"\nrm -rf \"$FFMPEG_EXTRACTED\"\n\n# Add FFmpeg to PATH in .bashrc or .bash_profile\nBASH_PROFILE=\"$HOME/.bashrc\"\nif [ ! -f \"$BASH_PROFILE\" ]; then\n  BASH_PROFILE=\"$HOME/.bash_profile\"\nfi\n\necho \"Adding FFmpeg to PATH...\"\nif ! grep -q \"$INSTALL_DIR\" \"$BASH_PROFILE\"; then\n  echo \"export PATH=\\\"\\$PATH:$INSTALL_DIR\\\"\" >> \"$BASH_PROFILE\"\nfi\n\n# Reload the shell configuration\nsource \"$BASH_PROFILE\"\n\n# Verify installation\necho \"Verifying FFmpeg installation...\"\nffmpeg -version > /dev/null 2>&1\nif [ $? -eq 0 ]; then\n  echo \"FFmpeg installed successfully! Version:\"\n  ffmpeg -version\nelse\n  echo \"Error: FFmpeg installation failed. Check if the binary is accessible.\"\n  exit 1\nfi\n\necho \"FFmpeg is installed in $INSTALL_DIR and added to your PATH.\"\necho \"You can now use 'ffmpeg' from any Bash terminal.\""}}
{"time":"2025-05-27T16:06:14.150Z","event":"INFO","data":{"message":"Task created successfully","taskId":20,"type":"shell"}}
{"time":"2025-05-27T16:06:14.150Z","event":"INFO","data":{"message":"Successfully parsed task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\tasks\\~syncthing~test-task2 2.md.tmp","type":"shell","description":"Test shell command","shell_command":"#!/bin/bash\n\n# Script to download and install FFmpeg binary on Windows\n\n# Define variables\nFFMPEG_URL=\"https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip\"\nDOWNLOAD_DIR=\"$HOME/ffmpeg\"\nINSTALL_DIR=\"$HOME/ffmpeg/bin\"\nZIP_FILE=\"$HOME/ffmpeg-release-essentials.zip\"\n\n# Create download directory\nmkdir -p \"$DOWNLOAD_DIR\"\n\n# Download FFmpeg binary\necho \"Downloading FFmpeg binary...\"\ncurl -L \"$FFMPEG_URL\" -o \"$ZIP_FILE\"\n\n# Check if download was successful\nif [ $? -ne 0 ]; then\n  echo \"Error: Failed to download FFmpeg. Check your internet connection.\"\n  exit 1\nfi\n\n# Unzip the downloaded file\necho \"Extracting FFmpeg...\"\nunzip -o \"$ZIP_FILE\" -d \"$DOWNLOAD_DIR\"\n\n# Check if unzip was successful\nif [ $? -ne 0 ]; then\n  echo \"Error: Failed to unzip FFmpeg. Ensure 'unzip' is installed.\"\n  exit 1\nfi\n\n# Find the extracted FFmpeg folder (name may vary, e.g., ffmpeg-7.0-essentials_build)\nFFMPEG_EXTRACTED=$(find \"$DOWNLOAD_DIR\" -maxdepth 1 -type d -name \"ffmpeg-*\" | head -n 1)\nif [ -z \"$FFMPEG_EXTRACTED\" ]; then\n  echo \"Error: Could not find extracted FFmpeg folder.\"\n  exit 1\nfi\n\n# Move the bin directory to the desired install location\nmv \"$FFMPEG_EXTRACTED/bin\" \"$INSTALL_DIR\"\nif [ $? -ne 0 ]; then\n  echo \"Error: Failed to move FFmpeg binaries.\"\n  exit 1\nfi\n\n# Clean up\nrm \"$ZIP_FILE\"\nrm -rf \"$FFMPEG_EXTRACTED\"\n\n# Add FFmpeg to PATH in .bashrc or .bash_profile\nBASH_PROFILE=\"$HOME/.bashrc\"\nif [ ! -f \"$BASH_PROFILE\" ]; then\n  BASH_PROFILE=\"$HOME/.bash_profile\"\nfi\n\necho \"Adding FFmpeg to PATH...\"\nif ! grep -q \"$INSTALL_DIR\" \"$BASH_PROFILE\"; then\n  echo \"export PATH=\\\"\\$PATH:$INSTALL_DIR\\\"\" >> \"$BASH_PROFILE\"\nfi\n\n# Reload the shell configuration\nsource \"$BASH_PROFILE\"\n\n# Verify installation\necho \"Verifying FFmpeg installation...\"\nffmpeg -version > /dev/null 2>&1\nif [ $? -eq 0 ]; then\n  echo \"FFmpeg installed successfully! Version:\"\n  ffmpeg -version\nelse\n  echo \"Error: FFmpeg installation failed. Check if the binary is accessible.\"\n  exit 1\nfi\n\necho \"FFmpeg is installed in $INSTALL_DIR and added to your PATH.\"\necho \"You can now use 'ffmpeg' from any Bash terminal.\""}}
{"time":"2025-05-27T16:06:14.155Z","event":"INFO","data":{"message":"Task created successfully","taskId":21,"type":"shell"}}
{"time":"2025-05-27T16:06:15.505Z","event":"INFO","data":{"message":"Starting shell task","taskId":19}}
{"time":"2025-05-27T16:06:15.562Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":19,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-19-shell-output.txt"}}
{"time":"2025-05-27T16:06:15.567Z","event":"INFO","data":{"message":"Shell task completed","taskId":19,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-19-shell-output.txt"}}
{"time":"2025-05-27T16:06:15.570Z","event":"INFO","data":{"message":"Starting shell task","taskId":20}}
{"time":"2025-05-27T16:06:20.513Z","event":"INFO","data":{"message":"Starting shell task","taskId":21}}
{"time":"2025-05-27T16:07:17.399Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":21,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-21-shell-output.txt"}}
{"time":"2025-05-27T16:07:17.404Z","event":"INFO","data":{"message":"Shell task completed","taskId":21,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-21-shell-output.txt"}}
{"time":"2025-05-27T16:07:23.657Z","event":"INFO","data":{"message":"Shell task executed successfully","taskId":20,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-20-shell-output.txt"}}
{"time":"2025-05-27T16:07:23.661Z","event":"INFO","data":{"message":"Shell task completed","taskId":20,"outputPath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs\\task-20-shell-output.txt"}}
{"time":"2025-05-27T16:07:23.665Z","event":"INFO","data":{"message":"Starting shell task","taskId":21}}
{"time":"2025-05-27T16:07:58.336Z","event":"ERROR","data":{"message":"Shell task failed","taskId":21,"exitCode":1,"stderr":"  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\r\n                                 Dload  Upload   Total   Spent    Left  Speed\r\n\r  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\r100   284  100   284    0     0   1880      0 --:--:-- --:--:-- --:--:--  1893\r\n\r  0 87.9M    0  575k    0     0   842k      0  0:01:46 --:--:--  0:01:46  842k\r 13 87.9M   13 11.9M    0     0  7301k      0  0:00:12  0:00:01  0:00:11 11.4M\r 17 87.9M   17 15.7M    0     0  6020k      0  0:00:14  0:00:02  0:00:12 7791k\r 21 87.9M   21 18.5M    0     0  5171k      0  0:00:17  0:00:03  0:00:14 6155k\r 27 87.9M   27 24.2M    0     0  5316k      0  0:00:16  0:00:04  0:00:12 6081k\r 28 87.9M   28 25.0M    0     0  4506k      0  0:00:19  0:00:05  0:00:14 5006k\r 32 87.9M   32 28.2M    0     0  4337k      0  0:00:20  0:00:06  0:00:14 3340k\r 35 87.9M   35 31.0M    0     0  4138k      0  0:00:21  0:00:07  0:00:14 3131k\r 38 87.9M   38 33.6M    0     0  3970k      0  0:00:22  0:00:08  0:00:14 3088k\r 38 87.9M   38 34.1M    0     0  3606k      0  0:00:24  0:00:09  0:00:15 2011k\r 39 87.9M   39 35.1M    0     0  3370k      0  0:00:26  0:00:10  0:00:16 2078k\r 42 87.9M   42 37.6M    0     0  3302k      0  0:00:27  0:00:11  0:00:16 1922k\r 43 87.9M   43 38.0M    0     0  3066k      0  0:00:29  0:00:12  0:00:17 1422k\r 45 87.9M   45 40.0M    0     0  2998k      0  0:00:30  0:00:13  0:00:17 1308k\r 49 87.9M   49 43.3M    0     0  3021k      0  0:00:29  0:00:14  0:00:15 1885k\r 52 87.9M   52 46.3M    0     0  3026k      0  0:00:29  0:00:15  0:00:14 2292k\r 57 87.9M   57 50.8M    0     0  3119k      0  0:00:28  0:00:16  0:00:12 2690k\r 61 87.9M   61 53.7M    0     0  3113k      0  0:00:28  0:00:17  0:00:11 3234k\r 62 87.9M   62 54.9M    0     0  3010k      0  0:00:29  0:00:18  0:00:11 3044k\r 63 87.9M   63 56.1M    0     0  2919k      0  0:00:30  0:00:19  0:00:11 2620k\r 68 87.9M   68 59.9M    0     0  2963k      0  0:00:30  0:00:20  0:00:10 2767k\r 68 87.9M   68 60.4M    0     0  2856k      0  0:00:31  0:00:21  0:00:10 1979k\r 69 87.9M   69 60.7M    0     0  2742k      0  0:00:32  0:00:22  0:00:10 1429k\r 70 87.9M   70 61.7M    0     0  2665k      0  0:00:33  0:00:23  0:00:10 1380k\r 71 87.9M   71 62.7M    0     0  2600k      0  0:00:34  0:00:24  0:00:10 1349k\r 73 87.9M   73 64.4M    0     0  2568k      0  0:00:35  0:00:25  0:00:10  925k\r 75 87.9M   75 66.1M    0     0  2539k      0  0:00:35  0:00:26  0:00:09 1164k\r 76 87.9M   76 66.9M    0     0  2475k      0  0:00:36  0:00:27  0:00:09 1265k\r 78 87.9M   78 69.1M    0     0  2469k      0  0:00:36  0:00:28  0:00:08 1536k\r 85 87.9M   85 75.5M    0     0  2605k      0  0:00:34  0:00:29  0:00:05 2625k\r 98 87.9M   98 86.7M    0     0  2895k      0  0:00:31  0:00:30  0:00:01 4570k\r100 87.9M  100 87.9M    0     0  2925k      0  0:00:30  0:00:30 --:--:-- 5426k\r\nmv: cannot move '/c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/bin' to '/c/Users/<USER>/ffmpeg/bin/bin': Directory not empty\n"}}
{"time":"2025-05-27T16:07:58.342Z","event":"ERROR","data":{"message":"Shell task errored","taskId":21,"error":"  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\r\n                                 Dload  Upload   Total   Spent    Left  Speed\r\n\r  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\r100   284  100   284    0     0   1880      0 --:--:-- --:--:-- --:--:--  1893\r\n\r  0 87.9M    0  575k    0     0   842k      0  0:01:46 --:--:--  0:01:46  842k\r 13 87.9M   13 11.9M    0     0  7301k      0  0:00:12  0:00:01  0:00:11 11.4M\r 17 87.9M   17 15.7M    0     0  6020k      0  0:00:14  0:00:02  0:00:12 7791k\r 21 87.9M   21 18.5M    0     0  5171k      0  0:00:17  0:00:03  0:00:14 6155k\r 27 87.9M   27 24.2M    0     0  5316k      0  0:00:16  0:00:04  0:00:12 6081k\r 28 87.9M   28 25.0M    0     0  4506k      0  0:00:19  0:00:05  0:00:14 5006k\r 32 87.9M   32 28.2M    0     0  4337k      0  0:00:20  0:00:06  0:00:14 3340k\r 35 87.9M   35 31.0M    0     0  4138k      0  0:00:21  0:00:07  0:00:14 3131k\r 38 87.9M   38 33.6M    0     0  3970k      0  0:00:22  0:00:08  0:00:14 3088k\r 38 87.9M   38 34.1M    0     0  3606k      0  0:00:24  0:00:09  0:00:15 2011k\r 39 87.9M   39 35.1M    0     0  3370k      0  0:00:26  0:00:10  0:00:16 2078k\r 42 87.9M   42 37.6M    0     0  3302k      0  0:00:27  0:00:11  0:00:16 1922k\r 43 87.9M   43 38.0M    0     0  3066k      0  0:00:29  0:00:12  0:00:17 1422k\r 45 87.9M   45 40.0M    0     0  2998k      0  0:00:30  0:00:13  0:00:17 1308k\r 49 87.9M   49 43.3M    0     0  3021k      0  0:00:29  0:00:14  0:00:15 1885k\r 52 87.9M   52 46.3M    0     0  3026k      0  0:00:29  0:00:15  0:00:14 2292k\r 57 87.9M   57 50.8M    0     0  3119k      0  0:00:28  0:00:16  0:00:12 2690k\r 61 87.9M   61 53.7M    0     0  3113k      0  0:00:28  0:00:17  0:00:11 3234k\r 62 87.9M   62 54.9M    0     0  3010k      0  0:00:29  0:00:18  0:00:11 3044k\r 63 87.9M   63 56.1M    0     0  2919k      0  0:00:30  0:00:19  0:00:11 2620k\r 68 87.9M   68 59.9M    0     0  2963k      0  0:00:30  0:00:20  0:00:10 2767k\r 68 87.9M   68 60.4M    0     0  2856k      0  0:00:31  0:00:21  0:00:10 1979k\r 69 87.9M   69 60.7M    0     0  2742k      0  0:00:32  0:00:22  0:00:10 1429k\r 70 87.9M   70 61.7M    0     0  2665k      0  0:00:33  0:00:23  0:00:10 1380k\r 71 87.9M   71 62.7M    0     0  2600k      0  0:00:34  0:00:24  0:00:10 1349k\r 73 87.9M   73 64.4M    0     0  2568k      0  0:00:35  0:00:25  0:00:10  925k\r 75 87.9M   75 66.1M    0     0  2539k      0  0:00:35  0:00:26  0:00:09 1164k\r 76 87.9M   76 66.9M    0     0  2475k      0  0:00:36  0:00:27  0:00:09 1265k\r 78 87.9M   78 69.1M    0     0  2469k      0  0:00:36  0:00:28  0:00:08 1536k\r 85 87.9M   85 75.5M    0     0  2605k      0  0:00:34  0:00:29  0:00:05 2625k\r 98 87.9M   98 86.7M    0     0  2895k      0  0:00:31  0:00:30  0:00:01 4570k\r100 87.9M  100 87.9M    0     0  2925k      0  0:00:30  0:00:30 --:--:-- 5426k\r\nmv: cannot move '/c/Users/<USER>/ffmpeg/ffmpeg-7.1.1-essentials_build/bin' to '/c/Users/<USER>/ffmpeg/bin/bin': Directory not empty\n"}}
