<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 -35 1301.46875 839" style="max-width: 1301.47px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_Tools_Orchestrator_0" d="M199.391,271L203.557,271C207.724,271,216.057,271,223.724,271C231.391,271,238.391,271,241.891,271L245.391,271"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_MediaProc_0" d="M348.821,298L367.426,376.5C386.032,455,423.242,612,445.348,690.5C467.453,769,474.453,769,477.953,769L481.453,769"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MediaProc_AI_Services_0" d="M666.094,769L670.26,769C674.427,769,682.76,769,690.427,769C698.094,769,705.094,769,708.594,769L712.094,769"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI_Services_SearchEngines_0" d="M852.969,769L857.135,769C861.302,769,869.635,769,885.103,769C900.57,769,923.172,769,934.473,769L945.773,769"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_Database_0" d="M353.116,244L371.006,198.833C388.895,153.667,424.674,63.333,461.784,18.167C498.893,-27,537.333,-27,575.773,-27C614.214,-27,652.654,-27,687.447,-27C722.24,-27,753.385,-27,784.531,-27C815.677,-27,846.823,-27,888.229,-27C929.635,-27,981.302,-27,1032.969,-27C1084.635,-27,1136.302,-27,1171.994,34.659C1207.686,96.317,1227.402,219.634,1237.261,281.293L1247.119,342.951"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_Analytics_0" d="M360.737,244L377.356,219.5C393.976,195,427.214,146,463.054,121.5C498.893,97,537.333,97,575.773,97C614.214,97,652.654,97,687.447,97C722.24,97,753.385,97,784.531,97C815.677,97,846.823,97,873.212,97C899.602,97,921.234,97,932.051,97L942.867,97"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Analytics_Database_0" d="M1119.07,97L1130.553,97C1142.036,97,1165.003,97,1185.902,138.017C1206.802,179.033,1225.635,261.067,1235.052,302.083L1244.468,343.1"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_Scheduler_0" d="M397.367,244L407.882,238.833C418.396,233.667,439.425,223.333,469.159,218.167C498.893,213,537.333,213,575.773,213C614.214,213,652.654,213,687.447,213C722.24,213,753.385,213,784.531,213C815.677,213,846.823,213,865.896,213C884.969,213,891.969,213,895.469,213L898.969,213"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_Database_0" d="M1162.969,213L1167.135,213C1171.302,213,1179.635,213,1192.223,234.771C1204.811,256.543,1221.654,300.085,1230.075,321.857L1238.497,343.628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_LearningSvc_0" d="M397.367,298L407.882,303.167C418.396,308.333,439.425,318.667,469.159,323.833C498.893,329,537.333,329,575.773,329C614.214,329,652.654,329,687.447,329C722.24,329,753.385,329,784.531,329C815.677,329,846.823,329,867.397,329C887.971,329,897.974,329,902.975,329L907.977,329"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LearningSvc_Database_0" d="M1153.961,329L1159.629,329C1165.297,329,1176.633,329,1186.038,333.443C1195.444,337.885,1202.919,346.77,1206.656,351.213L1210.394,355.656"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_FeedbackSvc_0" d="M362.094,298L378.487,320.5C394.88,343,427.667,388,463.28,410.5C498.893,433,537.333,433,575.773,433C614.214,433,652.654,433,687.447,433C722.24,433,753.385,433,784.531,433C815.677,433,846.823,433,867.141,433C887.458,433,896.948,433,901.693,433L906.438,433"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FeedbackSvc_Database_0" d="M1155.5,433L1160.911,433C1166.323,433,1177.146,433,1186.295,428.557C1195.444,424.115,1202.919,415.23,1206.656,410.787L1210.394,406.344"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_Monitoring_0" d="M353.885,298L371.647,339.833C389.408,381.667,424.931,465.333,461.912,507.167C498.893,549,537.333,549,575.773,549C614.214,549,652.654,549,687.447,549C722.24,549,753.385,549,784.531,549C815.677,549,846.823,549,865.896,549C884.969,549,891.969,549,895.469,549L898.969,549"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Monitoring_Database_0" d="M1162.969,549L1167.135,549C1171.302,549,1179.635,549,1192.223,527.229C1204.811,505.457,1221.654,461.915,1230.075,440.143L1238.497,418.372"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_RetryManager_0" d="M350.51,298L368.834,359.167C387.158,420.333,423.806,542.667,461.349,603.833C498.893,665,537.333,665,575.773,665C614.214,665,652.654,665,687.447,665C722.24,665,753.385,665,784.531,665C815.677,665,846.823,665,874.017,665C901.211,665,924.453,665,936.074,665L947.695,665"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RetryManager_Database_0" d="M1114.242,665L1126.53,665C1138.818,665,1163.393,665,1185.098,623.983C1206.802,582.967,1225.635,500.933,1235.052,459.917L1244.468,418.9"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_MCP_Servers_0" d="M354.822,244L372.427,205.667C390.032,167.333,425.243,90.667,449.222,53.494C473.201,16.321,485.949,18.643,492.323,19.804L498.698,20.964"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MCP_Servers_Orchestrator_0" d="M502.633,48.319L495.603,49.599C488.573,50.879,474.513,53.44,450.602,85.469C426.692,117.498,392.931,178.996,376.05,209.745L359.169,240.494"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SearchEngines_Database_0" d="M1116.164,769L1128.132,769C1140.099,769,1164.034,769,1185.807,710.672C1207.581,652.345,1227.193,535.689,1237,477.362L1246.806,419.034"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(103.6953125, 271)" id="flowchart-CLI_Tools-0" class="node default"><rect height="54" width="191.390625" y="-27" x="-95.6953125" style="" class="basic label-container"/><g transform="translate(-65.6953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="131.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CLI Tools &amp; Scripts</p></span></div></foreignObject></g></g><g transform="translate(342.421875, 271)" id="flowchart-Orchestrator-1" class="node default"><rect height="54" width="186.0625" y="-27" x="-93.03125" style="" class="basic label-container"/><g transform="translate(-63.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="126.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Orchestrator</p></span></div></foreignObject></g></g><g transform="translate(575.7734375, 769)" id="flowchart-MediaProc-2" class="node default"><rect height="54" width="180.640625" y="-27" x="-90.3203125" style="" class="basic label-container"/><g transform="translate(-60.3203125, -12)" style="" class="label"><rect/><foreignObject height="24" width="120.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Media Processors</p></span></div></foreignObject></g></g><g transform="translate(784.53125, 769)" id="flowchart-AI_Services-3" class="node default"><rect height="54" width="136.875" y="-27" x="-68.4375" style="" class="basic label-container"/><g transform="translate(-38.4375, -12)" style="" class="label"><rect/><foreignObject height="24" width="76.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI Services</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 769)" id="flowchart-SearchEngines-4" class="node default"><rect height="54" width="166.390625" y="-27" x="-83.1953125" style="" class="basic label-container"/><g transform="translate(-53.1953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="106.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Search Engines</p></span></div></foreignObject></g></g><g transform="translate(1253.21875, 381)" id="flowchart-Database-5" class="node default"><path transform="translate(-40.25, -34.189781021897815)" style="" class="basic label-container" d="M0,9.793187347931873 a40.25,9.793187347931873 0,0,0 80.5,0 a40.25,9.793187347931873 0,0,0 -80.5,0 l0,48.79318734793188 a40.25,9.793187347931873 0,0,0 80.5,0 l0,-48.79318734793188"/><g transform="translate(-32.75, -2)" style="" class="label"><rect/><foreignObject height="24" width="65.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 97)" id="flowchart-Analytics-6" class="node default"><rect height="54" width="172.203125" y="-27" x="-86.1015625" style="" class="basic label-container"/><g transform="translate(-56.1015625, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AnalyticsLogger</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 213)" id="flowchart-Scheduler-7" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Scheduler &amp; Rules Engine</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 329)" id="flowchart-LearningSvc-8" class="node default"><rect height="54" width="241.984375" y="-27" x="-120.9921875" style="" class="basic label-container"/><g transform="translate(-90.9921875, -12)" style="" class="label"><rect/><foreignObject height="24" width="181.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EnhancedLearningService</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 433)" id="flowchart-FeedbackSvc-9" class="node default"><rect height="54" width="245.0625" y="-27" x="-122.53125" style="" class="basic label-container"/><g transform="translate(-92.53125, -12)" style="" class="label"><rect/><foreignObject height="24" width="185.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Feedback &amp; Patterns</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 549)" id="flowchart-Monitoring-10" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Live Monitoring &amp; Dashboard</p></span></div></foreignObject></g></g><g transform="translate(575.7734375, 35)" id="flowchart-MCP_Servers-11" class="node default"><rect height="54" width="146.28125" y="-27" x="-73.140625" style="" class="basic label-container"/><g transform="translate(-43.140625, -12)" style="" class="label"><rect/><foreignObject height="24" width="86.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MCP Servers</p></span></div></foreignObject></g></g><g transform="translate(1032.96875, 665)" id="flowchart-RetryManager-12" class="node default"><rect height="54" width="162.546875" y="-27" x="-81.2734375" style="" class="basic label-container"/><g transform="translate(-51.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="102.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Retry Manager</p></span></div></foreignObject></g></g></g></g></g></svg>