{"id": "user-behavior-analytics", "type": "tool", "description": "Analyze user behavior patterns (privacy-aware)", "tool": "mcp_call", "args": {"server": "user-behavior", "method": "analyze_user_interactions", "params": {"time_range_hours": 168, "interaction_types": ["search", "view", "tag_edit", "feedback"], "include_patterns": true}}, "schedule": {"cron": "0 5 * * 1", "max_instances": 1, "overlap_policy": "skip"}, "metadata": {"priority": "normal", "tags": ["monitoring", "user-behavior", "analytics", "privacy", "patterns"], "created_by": "example", "notes": "Weekly user behavior analytics every Monday at 5 AM. Analyzes 7 days of user interactions in a privacy-aware manner to identify usage patterns and optimization opportunities."}}