import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';
import {
    createDatabaseMock,
    TestDatabaseSetup,
    CommonTestSetup,
    MCPServerTestUtils,
    UserInteractionTestFactory,
    MCPResponseTestFactory
} from '../src/test-utils';

// Setup common mocks
const { mockLogger, mockDb } = CommonTestSetup.setupMockModules();
const { mockServer } = CommonTestSetup.setupMCPMocks();

describe('User Behavior Server', () => {
    beforeEach(async () => {
        // Setup test database with proper schema
        await TestDatabaseSetup.createTestTables(mockDb);
        await TestDatabaseSetup.seedTestData(mockDb);

        // Reset mocks
        mockLogger.info.mockClear();
        mockLogger.error.mockClear();
        mockLogger.warn.mockClear();
        mockLogger.debug.mockClear();

        // Setup mock handlers for MCP server
        mockServer.setRequestHandler.mockClear();
    });

    afterEach(async () => {
        await TestDatabaseSetup.cleanupTestData(mockDb);
    });

    describe('Tool Registration', () => {
        it('should register all required tools', async () => {
            // Import the server to trigger registration
            await import('../src/mcp/user-behavior-server');

            expect(mockServer.setRequestHandler).toHaveBeenCalledWith('list_tools', expect.any(Function));
            expect(mockServer.setRequestHandler).toHaveBeenCalledWith('call_tool', expect.any(Function));
        });

        it('should list available tools', async () => {
            await import('../src/mcp/user-behavior-server');

            const listToolsHandler = mockServer.setRequestHandler.mock.calls.find(
                call => call[0] === 'list_tools'
            )?.[1];

            if (listToolsHandler) {
                const toolsResponse = await listToolsHandler({});
                expect(toolsResponse.tools).toBeDefined();
                expect(Array.isArray(toolsResponse.tools)).toBe(true);
            }
        });
    });

    describe('Tool Execution', () => {
        let callToolHandler: Function;

        beforeEach(async () => {
            await import('../src/mcp/user-behavior-server');

            callToolHandler = mockServer.setRequestHandler.mock.calls.find(
                call => call[0] === 'call_tool'
            )?.[1];
        });

        it('should handle analyze_user_interactions tool', async () => {
            // Mock database query results
            mockDb.query.mockReturnValue({
                all: mock(() => [
                    UserInteractionTestFactory.createUserInteraction({
                        interaction_type: 'search',
                        query_text: 'test query',
                        results_count: 5
                    })
                ])
            });

            const request = {
                params: {
                    name: 'analyze_user_interactions',
                    arguments: {
                        time_range_days: 7,
                        interaction_types: ['search', 'view']
                    }
                }
            };

            if (callToolHandler) {
                const response = await callToolHandler(request);
                expect(response.content).toBeDefined();
                expect(response.content[0].type).toBe('text');

                const result = JSON.parse(response.content[0].text);
                expect(result.analysis_period_days).toBe(7);
                expect(result.total_interactions).toBeDefined();
            }
        });

        it('should analyze session patterns', async () => {
            const sessionStats = mockDb.query(`
                SELECT 
                    AVG(session_duration_ms) as avg_duration,
                    AVG(total_interactions) as avg_interactions,
                    COUNT(*) as total_sessions
                FROM user_sessions
            `).get() as any;
            
            expect(sessionStats.total_sessions).toBeGreaterThan(0);
            expect(sessionStats.avg_duration).toBeGreaterThan(0);
            expect(sessionStats.avg_interactions).toBeGreaterThan(0);
        });
    });

    describe('generate_personalization_recommendations', () => {
        it('should generate content recommendations', async () => {
            const userId = 'session_001';
            
            // Analyze user's viewing patterns
            const viewingHistory = mockDb.query(`
                SELECT interaction_data 
                FROM user_interactions 
                WHERE user_session_id = ? AND interaction_type = 'view'
            `).all(userId) as any[];
            
            const contentRecommendations = [
                {
                    type: 'content_discovery',
                    description: 'Personalized content feed based on viewing history',
                    confidence: 0.87,
                    implementation_priority: 'high',
                    expected_impact: 'Increase content engagement by 25%'
                },
                {
                    type: 'similar_content',
                    description: 'Show related content suggestions after viewing',
                    confidence: 0.82,
                    implementation_priority: 'medium',
                    expected_impact: 'Extend session duration by 15%'
                }
            ];
            
            expect(contentRecommendations.length).toBe(2);
            expect(contentRecommendations[0].confidence).toBeGreaterThan(0.8);
            expect(contentRecommendations[1].implementation_priority).toBe('medium');
        });

        it('should generate interface recommendations', async () => {
            const interfaceRecommendations = [
                {
                    type: 'layout_optimization',
                    description: 'Customize interface layout based on usage patterns',
                    confidence: 0.79,
                    implementation_priority: 'medium',
                    expected_impact: 'Reduce task completion time by 20%'
                },
                {
                    type: 'quick_actions',
                    description: 'Add quick action buttons for frequently used features',
                    confidence: 0.84,
                    implementation_priority: 'high',
                    expected_impact: 'Improve workflow efficiency by 30%'
                }
            ];
            
            expect(interfaceRecommendations.length).toBe(2);
            expect(interfaceRecommendations[1].confidence).toBeGreaterThan(0.8);
        });

        it('should prioritize recommendations by confidence', async () => {
            const recommendations = [
                { confidence: 0.95, priority: 'high' },
                { confidence: 0.75, priority: 'medium' },
                { confidence: 0.65, priority: 'low' },
                { confidence: 0.85, priority: 'high' }
            ];
            
            const sorted = recommendations.sort((a, b) => b.confidence - a.confidence);
            
            expect(sorted[0].confidence).toBe(0.95);
            expect(sorted[1].confidence).toBe(0.85);
            expect(sorted[2].confidence).toBe(0.75);
            expect(sorted[3].confidence).toBe(0.65);
        });
    });

    describe('identify_engagement_opportunities', () => {
        it('should calculate engagement metrics', async () => {
            const totalInteractions = mockDb.query('SELECT COUNT(*) as count FROM user_interactions').get() as any;
            const totalSessions = mockDb.query('SELECT COUNT(*) as count FROM user_sessions').get() as any;
            
            const avgInteractionsPerSession = totalInteractions.count / totalSessions.count;
            
            const engagementAnalysis = {
                engagement_score: Math.min(avgInteractionsPerSession / 10, 1), // Normalize to 0-1
                top_content_types: ['video', 'audio', 'documents'],
                peak_usage_times: ['9-11 AM', '2-4 PM', '7-9 PM'],
                interaction_patterns: [
                    {
                        pattern_type: 'session_length',
                        frequency: 25,
                        confidence: 0.82,
                        description: 'Users engage longer with multimedia content'
                    }
                ],
                drop_off_points: ['Complex search interface', 'Slow loading times']
            };
            
            expect(engagementAnalysis.engagement_score).toBeGreaterThanOrEqual(0);
            expect(engagementAnalysis.engagement_score).toBeLessThanOrEqual(1);
            expect(engagementAnalysis.top_content_types.length).toBe(3);
        });

        it('should identify drop-off points', async () => {
            // Analyze incomplete interactions
            const incompleteViews = mockDb.query(`
                SELECT interaction_data 
                FROM user_interactions 
                WHERE interaction_type = 'view'
            `).all() as any[];
            
            let lowCompletionCount = 0;
            for (const view of incompleteViews) {
                try {
                    const data = JSON.parse(view.interaction_data);
                    if (data.completion_percentage < 0.3) {
                        lowCompletionCount++;
                    }
                } catch (error) {
                    // Skip invalid JSON
                }
            }
            
            const dropOffRate = incompleteViews.length > 0 ? lowCompletionCount / incompleteViews.length : 0;
            expect(dropOffRate).toBeGreaterThanOrEqual(0);
            expect(dropOffRate).toBeLessThanOrEqual(1);
        });
    });

    describe('track_behavior_changes', () => {
        it('should compare baseline and recent periods', async () => {
            const baselineDays = 30;
            const comparisonDays = 7;
            
            const baselineStart = new Date(Date.now() - baselineDays * 24 * 60 * 60 * 1000);
            const comparisonStart = new Date(Date.now() - comparisonDays * 24 * 60 * 60 * 1000);
            
            // Baseline metrics
            const baselineMetrics = mockDb.query(`
                SELECT 
                    AVG(session_duration_ms) as avg_duration,
                    COUNT(*) / ? as daily_sessions
                FROM user_sessions 
                WHERE started_at BETWEEN ? AND ?
            `).get(baselineDays, baselineStart.toISOString(), comparisonStart.toISOString()) as any;
            
            // Recent metrics
            const recentMetrics = mockDb.query(`
                SELECT 
                    AVG(session_duration_ms) as avg_duration,
                    COUNT(*) / ? as daily_sessions
                FROM user_sessions 
                WHERE started_at > ?
            `).get(comparisonDays, comparisonStart.toISOString()) as any;
            
            const changes = {
                session_duration: {
                    baseline_average: baselineMetrics?.avg_duration || 0,
                    recent_average: recentMetrics?.avg_duration || 0,
                    trend: 'stable'
                },
                daily_sessions: {
                    baseline_average: baselineMetrics?.daily_sessions || 0,
                    recent_average: recentMetrics?.daily_sessions || 0,
                    trend: 'stable'
                }
            };
            
            // Determine trends
            if (changes.session_duration.recent_average > changes.session_duration.baseline_average * 1.1) {
                changes.session_duration.trend = 'improving';
            } else if (changes.session_duration.recent_average < changes.session_duration.baseline_average * 0.9) {
                changes.session_duration.trend = 'declining';
            }
            
            expect(changes.session_duration.baseline_average).toBeGreaterThanOrEqual(0);
            expect(changes.session_duration.recent_average).toBeGreaterThanOrEqual(0);
        });

        it('should calculate trend analysis', async () => {
            const mockChanges = {
                metric1: { trend: 'improving' },
                metric2: { trend: 'improving' },
                metric3: { trend: 'declining' },
                metric4: { trend: 'stable' }
            };
            
            const improving = Object.values(mockChanges).filter(change => change.trend === 'improving').length;
            const declining = Object.values(mockChanges).filter(change => change.trend === 'declining').length;
            const stable = Object.values(mockChanges).filter(change => change.trend === 'stable').length;
            
            const trendAnalysis = {
                overall_trend: improving > declining ? 'positive' : declining > improving ? 'negative' : 'stable',
                improving_metrics: improving,
                declining_metrics: declining,
                stable_metrics: stable
            };
            
            expect(trendAnalysis.improving_metrics).toBe(2);
            expect(trendAnalysis.declining_metrics).toBe(1);
            expect(trendAnalysis.stable_metrics).toBe(1);
            expect(trendAnalysis.overall_trend).toBe('positive');
        });
    });

    describe('predict_user_needs', () => {
        it('should generate predictions with confidence scores', async () => {
            const predictionCategories = ['content_interest', 'feature_usage', 'optimal_timing'];
            const confidenceThreshold = 0.7;
            
            const predictions = [];
            
            for (const category of predictionCategories) {
                const confidence = Math.random() * 0.4 + 0.6; // 0.6-1.0
                
                if (confidence >= confidenceThreshold) {
                    let prediction = '';
                    switch (category) {
                        case 'content_interest':
                            prediction = 'User likely to search for video content';
                            break;
                        case 'feature_usage':
                            prediction = 'User will likely use advanced search filters';
                            break;
                        case 'optimal_timing':
                            prediction = 'Best time for system maintenance';
                            break;
                    }
                    
                    predictions.push({
                        category,
                        prediction,
                        confidence,
                        time_window: 'next 4 hours'
                    });
                }
            }
            
            expect(predictions.length).toBeGreaterThanOrEqual(0);
            predictions.forEach(pred => {
                expect(pred.confidence).toBeGreaterThanOrEqual(confidenceThreshold);
            });
        });

        it('should filter predictions by confidence threshold', async () => {
            const samplePredictions = [
                { confidence: 0.95, prediction: 'High confidence prediction' },
                { confidence: 0.75, prediction: 'Medium confidence prediction' },
                { confidence: 0.55, prediction: 'Low confidence prediction' }
            ];
            
            const threshold = 0.7;
            const filteredPredictions = samplePredictions.filter(p => p.confidence >= threshold);
            
            expect(filteredPredictions.length).toBe(2);
            expect(filteredPredictions[0].confidence).toBe(0.95);
            expect(filteredPredictions[1].confidence).toBe(0.75);
        });
    });

    describe('Privacy and Data Handling', () => {
        it('should support anonymized data analysis', async () => {
            const privacyMode = true;
            
            if (privacyMode) {
                // In privacy mode, user IDs should be hashed or anonymized
                const sessions = mockDb.query('SELECT user_id FROM user_sessions').all() as any[];
                
                // Simulate anonymization
                const anonymizedSessions = sessions.map(session => ({
                    ...session,
                    user_id: `anon_${session.user_id.slice(-3)}` // Keep only last 3 chars
                }));
                
                expect(anonymizedSessions.length).toBe(sessions.length);
                anonymizedSessions.forEach(session => {
                    expect(session.user_id).toMatch(/^anon_/);
                });
            }
        });

        it('should handle data retention policies', async () => {
            const retentionDays = 90;
            const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
            
            // Count old interactions that should be cleaned up
            const oldInteractions = mockDb.query(`
                SELECT COUNT(*) as count 
                FROM user_interactions 
                WHERE created_at < ?
            `).get(cutoffDate.toISOString()) as any;
            
            expect(oldInteractions.count).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Error Handling', () => {
        it('should handle database errors gracefully', async () => {
            mockDb.close();
            
            const errorDb = {
                query: mock(() => {
                    throw new Error('Database connection failed');
                })
            };
            
            mockGetDatabase.mockReturnValue(errorDb);
            
            expect(() => {
                errorDb.query('SELECT * FROM user_interactions');
            }).toThrow('Database connection failed');
        });

        it('should handle invalid JSON in interaction data', async () => {
            // Insert interaction with invalid JSON
            mockDb.run(`
                INSERT INTO user_interactions (user_session_id, interaction_type, target_id, target_type, interaction_data, created_at)
                VALUES ('test_session', 'test', 'test_target', 'test', 'invalid json', datetime('now'))
            `);
            
            const interactions = mockDb.query(`
                SELECT interaction_data 
                FROM user_interactions 
                WHERE interaction_type = 'test'
            `).all() as any[];
            
            expect(interactions.length).toBe(1);
            
            // Should handle JSON parsing errors gracefully
            expect(() => {
                JSON.parse(interactions[0].interaction_data);
            }).toThrow();
        });
    });

    describe('Performance', () => {
        it('should handle large interaction datasets efficiently', async () => {
            // Insert many test interactions
            const insertStmt = mockDb.prepare(`
                INSERT INTO user_interactions (user_session_id, interaction_type, target_id, target_type, interaction_data, created_at, duration_ms)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `);
            
            const now = new Date();
            for (let i = 0; i < 1000; i++) {
                const interactionTime = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);
                
                insertStmt.run(
                    `session_${i % 10}`,
                    ['search', 'view', 'tag_edit', 'feedback'][i % 4],
                    `target_${i}`,
                    'media',
                    JSON.stringify({ test: true, index: i }),
                    interactionTime.toISOString(),
                    Math.floor(Math.random() * 10000) + 1000
                );
            }
            
            const totalInteractions = mockDb.query('SELECT COUNT(*) as count FROM user_interactions').get() as any;
            expect(totalInteractions.count).toBeGreaterThan(1000);
            
            // Test efficient querying with aggregation
            const hourlyStats = mockDb.query(`
                SELECT 
                    strftime('%H', created_at) as hour,
                    COUNT(*) as interaction_count,
                    AVG(duration_ms) as avg_duration
                FROM user_interactions 
                WHERE created_at > datetime('now', '-7 days')
                GROUP BY hour
                ORDER BY hour
            `).all();
            
            expect(hourlyStats.length).toBeLessThanOrEqual(24);
        });
    });
});
