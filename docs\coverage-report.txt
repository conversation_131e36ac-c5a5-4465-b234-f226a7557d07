bun test v1.2.14 (6a363a38)

test/search-logs.test.ts:

# Unhandled error between tests
-------------------------------
error: Cannot find package 'chromadb' from '/workspace/banana-bun/src/memory/embeddings.ts'
-------------------------------


test/analyze-cross-modal-cli.test.ts:
(pass) Cross-Modal CLI parseArgs > parses correlation args [0.39ms]
(pass) Cross-Modal CLI parseArgs > parses quality args [0.09ms]
(pass) Cross-Modal CLI parseArgs > parses search pattern args [0.11ms]
(pass) Cross-Modal CLI parseArgs > parses track-search args [0.09ms]
(pass) Cross-Modal CLI parseArgs > shows help when requested [0.14ms]
(pass) Cross-Modal CLI actions > runs correlation analysis [0.51ms]
(pass) Cross-Modal CLI actions > runs quality assessment [0.30ms]
(pass) Cross-Modal CLI actions > generates embeddings [0.40ms]
(pass) Cross-Modal CLI actions > analyzes search patterns [0.20ms]
(pass) Cross-Modal CLI actions > tracks search behavior [0.27ms]
(pass) Cross-Modal CLI actions > shows dashboard with report [0.27ms]

test/review-service.integration.test.ts:
126 |                 'Task completed successfully',
127 |                 'Output file exists',
128 |                 'No errors reported'
129 |             ];
130 | 
131 |             const result = await reviewService.reviewTask(1, criteria);
                                                     ^
TypeError: reviewService.reviewTask is not a function. (In 'reviewService.reviewTask(1, criteria)', 'reviewService.reviewTask' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:131:48)
(fail) Review Service > Task Review > should review completed task [0.74ms]
135 |             expect(result.passed_criteria).toBeDefined();
136 |             expect(result.failed_criteria).toBeDefined();
137 |         });
138 | 
139 |         it('should handle missing task', async () => {
140 |             const result = await reviewService.reviewTask(999, ['Task exists']);
                                                     ^
TypeError: reviewService.reviewTask is not a function. (In 'reviewService.reviewTask(999, ["Task exists"])', 'reviewService.reviewTask' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:140:48)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:139:42)
(fail) Review Service > Task Review > should handle missing task [0.17ms]
154 |                 'result_summary.includes("completed")',
155 |                 'result_summary.includes("error")', // This should fail
156 |                 'type === "shell"'
157 |             ];
158 | 
159 |             const result = await reviewService.reviewTask(1, criteria);
                                                     ^
TypeError: reviewService.reviewTask is not a function. (In 'reviewService.reviewTask(1, criteria)', 'reviewService.reviewTask' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:159:48)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:146:47)
(fail) Review Service > Task Review > should evaluate custom criteria [0.25ms]
184 |                 'Output file is valid Python',
185 |                 'File size > 50 bytes',
186 |                 'Contains function definition'
187 |             ];
188 | 
189 |             const result = await reviewService.reviewTask(1, criteria);
                                                     ^
TypeError: reviewService.reviewTask is not a function. (In 'reviewService.reviewTask(1, criteria)', 'reviewService.reviewTask' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:189:48)
(fail) Review Service > Task Review > should check output file quality [0.39ms]
205 |             `);
206 | 
207 |             const taskIds = [1, 2, 3];
208 |             const criteria = ['status === "completed"'];
209 | 
210 |             const result = await reviewService.reviewBatch(taskIds, criteria);
                                                     ^
TypeError: reviewService.reviewBatch is not a function. (In 'reviewService.reviewBatch(taskIds, criteria)', 'reviewService.reviewBatch' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:210:48)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:197:44)
(fail) Review Service > Batch Review > should review multiple tasks [0.25ms]
226 |                     (1, 'shell', 'Task 1', 'completed'),
227 |                     (2, 'shell', 'Task 2', 'completed'),
228 |                     (3, 'shell', 'Task 3', 'failed')
229 |             `);
230 | 
231 |             const result = await reviewService.generateSummaryReport([1, 2, 3]);
                                                     ^
TypeError: reviewService.generateSummaryReport is not a function. (In 'reviewService.generateSummaryReport([1, 2, 3])', 'reviewService.generateSummaryReport' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:231:48)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:222:46)
(fail) Review Service > Batch Review > should generate summary report [0.25ms]
250 | # Test the function
251 | for i in range(10):
252 | print(f"F({i}) = {calculate_fibonacci(i)}")
253 | `;
254 | 
255 |             const metrics = await reviewService.calculateCodeQuality(codeContent, 'python');
                                                      ^
TypeError: reviewService.calculateCodeQuality is not a function. (In 'reviewService.calculateCodeQuality(`
def calculate_fibonacci(n):
if n <= 1:
    return n
return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

# Test the function
for i in range(10):
print(f"F({i}) = {calculate_fibonacci(i)}")
`, "python")', 'reviewService.calculateCodeQuality' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:255:49)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:243:53)
(fail) Review Service > Quality Metrics > should calculate code quality metrics [0.18ms]
275 | 
276 | tar -czf "$BACKUP_DIR/backup_$(date +%Y%m%d).tar.gz" "$SOURCE_DIR"
277 | echo "Backup completed successfully"
278 | `;
279 | 
280 |             const metrics = await reviewService.calculateCodeQuality(shellContent, 'shell');
                                                      ^
TypeError: reviewService.calculateCodeQuality is not a function. (In 'reviewService.calculateCodeQuality(`#!/bin/bash
# Backup script with error handling

set -e  # Exit on error

BACKUP_DIR="/backup"
SOURCE_DIR="/data"

if [ ! -d "$BACKUP_DIR" ]; then
mkdir -p "$BACKUP_DIR"
fi

tar -czf "$BACKUP_DIR/backup_$(date +%Y%m%d).tar.gz" "$SOURCE_DIR"
echo "Backup completed successfully"
`, "shell")', 'reviewService.calculateCodeQuality' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:280:49)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:263:51)
(fail) Review Service > Quality Metrics > should analyze shell script quality [0.16ms]
291 |                 INSERT INTO tasks (id, type, description, status, started_at, finished_at)
292 |                 VALUES (1, 'shell', 'Performance test', 'completed',
293 |                        datetime('now', '-5 minutes'), datetime('now'))
294 |             `);
295 | 
296 |             const analysis = await reviewService.analyzePerformance(1);
                                                       ^
TypeError: reviewService.analyzePerformance is not a function. (In 'reviewService.analyzePerformance(1)', 'reviewService.analyzePerformance' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:296:50)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:289:57)
(fail) Review Service > Performance Analysis > should analyze task execution performance [0.22ms]
308 |                     ('shell', 'Similar task 1', 'completed', datetime('now', '-10 minutes'), datetime('now', '-8 minutes')),
309 |                     ('shell', 'Similar task 2', 'completed', datetime('now', '-15 minutes'), datetime('now', '-12 minutes')),
310 |                     ('shell', 'Test task', 'completed', datetime('now', '-5 minutes'), datetime('now'))
311 |             `);
312 | 
313 |             const comparison = await reviewService.comparePerformance('shell', 'Test task');
                                                         ^
TypeError: reviewService.comparePerformance is not a function. (In 'reviewService.comparePerformance("shell", "Test task")', 'reviewService.comparePerformance' is undefined)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:313:52)
      at <anonymous> (/workspace/banana-bun/test/review-service.integration.test.ts:303:61)
(fail) Review Service > Performance Analysis > should compare performance against benchmarks [0.22ms]

test/review-service.db.test.ts:
(pass) ReviewService Database Functions > summarizes task reviews [0.58ms]
(pass) ReviewService Database Functions > computes overall review metrics [0.27ms]
(pass) ReviewService Database Functions > retrieves task review history [0.18ms]
(pass) ReviewService Database Functions > retrieves recent reviews [0.15ms]
(pass) ReviewService Database Functions > badge helpers [0.16ms]

test/llm-planning-service.test.ts:
(pass) LlmPlanningService Core Functions > parses JSON responses from LLM [0.38ms]
(pass) LlmPlanningService Core Functions > falls back to text plan on invalid JSON [0.06ms]
(pass) LlmPlanningService Core Functions > builds planning prompts with context [0.22ms]
(pass) LlmPlanningService Core Functions > calculates optimization score with bonuses [0.19ms]
(pass) LlmPlanningService Core Functions > detects recurring error patterns [0.48ms]

test/embeddings.test.ts:
76 |         await embeddingManager.shutdown();
77 |     });
78 | 
79 |     describe('Initialization', () => {
80 |         it('should initialize successfully', async () => {
81 |             await embeddingManager.initialize();
                                        ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:81:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:80:46)
(fail) Embeddings Manager > Initialization > should initialize successfully [0.19ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
76 |         await embeddingManager.shutdown();
77 |     });
78 | 
79 |     describe('Initialization', () => {
80 |         it('should initialize successfully', async () => {
81 |             await embeddingManager.initialize();
                                        ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:81:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:80:46)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

89 |         });
90 | 
91 |         it('should create collection if it does not exist', async () => {
92 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Collection not found'));
93 | 
94 |             await embeddingManager.initialize();
                                        ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:94:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:91:61)
(fail) Embeddings Manager > Initialization > should create collection if it does not exist [0.13ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
87 |                 expect.stringContaining('Embedding manager initialized')
88 |             );
89 |         });
90 | 
91 |         it('should create collection if it does not exist', async () => {
92 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Collection not found'));
                                                                      ^
error: Collection not found
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:92:66)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:91:61)
-------------------------------


# Unhandled error between tests
-------------------------------
89 |         });
90 | 
91 |         it('should create collection if it does not exist', async () => {
92 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Collection not found'));
93 | 
94 |             await embeddingManager.initialize();
                                        ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:94:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:91:61)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

101 | 
102 |         it('should handle initialization errors', async () => {
103 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Connection failed'));
104 |             mockChromaClient.createCollection.mockRejectedValueOnce(new Error('Creation failed'));
105 | 
106 |             await expect(embeddingManager.initialize()).rejects.toThrow();
                                                ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:106:43)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:102:51)
(fail) Embeddings Manager > Initialization > should handle initialization errors [0.10ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
 98 |                 embeddingFunction: expect.any(Object)
 99 |             });
100 |         });
101 | 
102 |         it('should handle initialization errors', async () => {
103 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Connection failed'));
                                                                       ^
error: Connection failed
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:103:66)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:102:51)
-------------------------------


# Unhandled error between tests
-------------------------------
 99 |             });
100 |         });
101 | 
102 |         it('should handle initialization errors', async () => {
103 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Connection failed'));
104 |             mockChromaClient.createCollection.mockRejectedValueOnce(new Error('Creation failed'));
                                                                          ^
error: Creation failed
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:104:69)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:102:51)
-------------------------------


# Unhandled error between tests
-------------------------------
101 | 
102 |         it('should handle initialization errors', async () => {
103 |             mockChromaClient.getCollection.mockRejectedValueOnce(new Error('Connection failed'));
104 |             mockChromaClient.createCollection.mockRejectedValueOnce(new Error('Creation failed'));
105 | 
106 |             await expect(embeddingManager.initialize()).rejects.toThrow();
                                                ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:106:43)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:102:51)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

107 |         });
108 |     });
109 | 
110 |     describe('Adding Task Embeddings', () => {
111 |         beforeEach(async () => {
112 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:112:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:111:20)
(fail) Embeddings Manager > Adding Task Embeddings > should add task embedding successfully [112790.98ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Adding Task Embeddings > should add task embedding successfully

# Unhandled error between tests
-------------------------------
107 |         });
108 |     });
109 | 
110 |     describe('Adding Task Embeddings', () => {
111 |         beforeEach(async () => {
112 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:112:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:111:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

107 |         });
108 |     });
109 | 
110 |     describe('Adding Task Embeddings', () => {
111 |         beforeEach(async () => {
112 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:112:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:111:20)
(fail) Embeddings Manager > Adding Task Embeddings > should handle metadata serialization [112791.08ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Adding Task Embeddings > should handle metadata serialization

# Unhandled error between tests
-------------------------------
107 |         });
108 |     });
109 | 
110 |     describe('Adding Task Embeddings', () => {
111 |         beforeEach(async () => {
112 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:112:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:111:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

107 |         });
108 |     });
109 | 
110 |     describe('Adding Task Embeddings', () => {
111 |         beforeEach(async () => {
112 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:112:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:111:20)
(fail) Embeddings Manager > Adding Task Embeddings > should handle embedding errors gracefully [112791.19ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > pX  andle embedding errors gracefully

# Unhandled error between tests
-------------------------------
107 |         });
108 |     });
109 | 
110 |     describe('Adding Task Embeddings', () => {
111 |         beforeEach(async () => {
112 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:112:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:111:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
(fail) Embeddings Manager > Finding Similar Tasks > should find similar tasks successfully [112791.32ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Finding Similar Tasks > should find similar tasks successfully

# Unhandled error between tests
-------------------------------
173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
(fail) Embeddings Manager > Finding Similar Tasks > should handle empty results [112791.43ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Finding Similar Tasks > should handle empty results

# Unhandled error between tests
-------------------------------
173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
(fail) Embeddings Manager > Finding Similar Tasks > should filter by task type [112791.55ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Finding Similar Tasks > should filter by task type

# Unhandled error between tests
-------------------------------
173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
(fail) Embeddings Manager > Finding Similar Tasks > should calculate similarity scores correctly [112791.65ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Finding Similar Tasks > should calculate similarity scores correctly

# Unhandled error between tests
-------------------------------
173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
(fail) Embeddings Manager > Finding Similar Tasks > should handle query errors [112791.74ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > X  andle query errors

# Unhandled error between tests
-------------------------------
173 |         });
174 |     });
175 | 
176 |     describe('Finding Similar Tasks', () => {
177 |         beforeEach(async () => {
178 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:178:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:177:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

249 |         });
250 |     });
251 | 
252 |     describe('Task Embedding Management', () => {
253 |         beforeEach(async () => {
254 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:254:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:253:20)
(fail) Embeddings Manager > Task Embedding Management > should delete task embedding [112791.90ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Task Embedding Management > should delete task embedding

# Unhandled error between tests
-------------------------------
249 |         });
250 |     });
251 | 
252 |     describe('Task Embedding Management', () => {
253 |         beforeEach(async () => {
254 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:254:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:253:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

249 |         });
250 |     });
251 | 
252 |     describe('Task Embedding Management', () => {
253 |         beforeEach(async () => {
254 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:254:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:253:20)
(fail) Embeddings Manager > Task Embedding Management > should get collection statistics [112792.01ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager > Task Embedding Management > should get collection statistics

# Unhandled error between tests
-------------------------------
249 |         });
250 |     });
251 | 
252 |     describe('Task Embedding Management', () => {
253 |         beforeEach(async () => {
254 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:254:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:253:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

249 |         });
250 |     });
251 | 
252 |     describe('Task Embedding Management', () => {
253 |         beforeEach(async () => {
254 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:254:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:253:20)
(fail) Embeddings Manager > Task Embedding Management > should clear all embeddings [112792.10ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

(fail) Embeddings Manager >  X  lear all embeddings

# Unhandled error between tests
-------------------------------
249 |         });
250 |     });
251 | 
252 |     describe('Task Embedding Management', () => {
253 |         beforeEach(async () => {
254 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:254:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:253:20)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

290 |         });
291 |     });
292 | 
293 |     describe('Shutdown', () => {
294 |         it('should shutdown gracefully', async () => {
295 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:295:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:294:42)
(fail) Embeddings Manager > Shutdown > should shutdown gracefully [0.16ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
290 |         });
291 |     });
292 | 
293 |     describe('Shutdown', () => {
294 |         it('should shutdown gracefully', async () => {
295 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:295:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:294:42)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

300 |             );
301 |         });
302 | 
303 |         it('should handle shutdown without initialization', async () => {
304 |             // Should not throw
305 |             await embeddingManager.shutdown();
                                         ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:305:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:303:61)
(fail) Embeddings Manager > Shutdown > should handle shutdown without initialization [0.08ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
300 |             );
301 |         });
302 | 
303 |         it('should handle shutdown without initialization', async () => {
304 |             // Should not throw
305 |             await embeddingManager.shutdown();
                                         ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:305:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:303:61)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

308 | 
309 |     describe('Error Handling', () => {
310 |         it('should handle network connectivity issues', async () => {
311 |             mockChromaClient.getCollection.mockRejectedValue(new Error('Network error'));
312 | 
313 |             await expect(embeddingManager.initialize()).rejects.toThrow('Network error');
                                                ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:313:43)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:310:57)
(fail) Embeddings Manager > Error Handling > should handle network connectivity issues [0.10ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
306 |         });
307 |     });
308 | 
309 |     describe('Error Handling', () => {
310 |         it('should handle network connectivity issues', async () => {
311 |             mockChromaClient.getCollection.mockRejectedValue(new Error('Network error'));
                                                                   ^
error: Network error
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:311:62)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:310:57)
-------------------------------


# Unhandled error between tests
-------------------------------
308 | 
309 |     describe('Error Handling', () => {
310 |         it('should handle network connectivity issues', async () => {
311 |             mockChromaClient.getCollection.mockRejectedValue(new Error('Network error'));
312 | 
313 |             await expect(embeddingManager.initialize()).rejects.toThrow('Network error');
                                                ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:313:43)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:310:57)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------

312 | 
313 |             await expect(embeddingManager.initialize()).rejects.toThrow('Network error');
314 |         });
315 | 
316 |         it('should handle malformed responses', async () => {
317 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:317:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:316:49)
(fail) Embeddings Manager > Error Handling > should handle malformed responses [0.15ms]

# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


# Unhandled error between tests
-------------------------------
312 | 
313 |             await expect(embeddingManager.initialize()).rejects.toThrow('Network error');
314 |         });
315 | 
316 |         it('should handle malformed responses', async () => {
317 |             await embeddingManager.initialize();
                                         ^
TypeError: embeddingManager.initialize is not a function. (In 'embeddingManager.initialize()', 'embeddingManager.initialize' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:317:36)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:316:49)
-------------------------------


# Unhandled error between tests
-------------------------------
71 |             }
72 |         });
73 |     });
74 | 
75 |     afterEach(async () => {
76 |         await embeddingManager.shutdown();
                                    ^
TypeError: embeddingManager.shutdown is not a function. (In 'embeddingManager.shutdown()', 'embeddingManager.shutdown' is undefined)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:76:32)
      at <anonymous> (/workspace/banana-bun/test/embeddings.test.ts:75:15)
-------------------------------


test/scheduler.test.ts:
20 |                     '0 0 1 * *',     // First day of month
21 |                     '0 0 * * 0'      // Every Sunday
22 |                 ];
23 | 
24 |                 validExpressions.forEach(expr => {
25 |                     expect(() => parser.parse(expr)).not.toThrow();
                                                              ^
error: expect(received).not.toThrow()

Error name: "TypeError"
Error message: "parser.parse is not a function. (In 'parser.parse(expr)', 'parser.parse' is undefined)"

      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:25:58)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:24:34)
(fail) Scheduler System > CronParser > Cron Expression Parsing > should parse valid cron expressions [0.22ms]
(pass) Scheduler System > CronParser > Cron Expression Parsing > should reject invalid cron expressions [0.09ms]
43 | 
44 |             it('should calculate next execution time correctly', () => {
45 |                 const now = new Date('2024-01-01T10:00:00Z');
46 | 
47 |                 // Every hour at minute 0
48 |                 const nextHour = parser.getNextExecution('0 * * * *', now);
                                             ^
TypeError: parser.getNextExecution is not a function. (In 'parser.getNextExecution("0 * * * *", now)', 'parser.getNextExecution' is undefined)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:48:41)
(fail) Scheduler System > CronParser > Cron Expression Parsing > should calculate next execution time correctly [0.10ms]
58 | 
59 |             it('should handle timezone considerations', () => {
60 |                 const now = new Date('2024-01-01T10:00:00Z');
61 |                 const timezone = 'America/New_York';
62 | 
63 |                 const nextExecution = parser.getNextExecution('0 9 * * *', now, timezone);
                                                  ^
TypeError: parser.getNextExecution is not a function. (In 'parser.getNextExecution("0 9 * * *", now, "America/New_York")', 'parser.getNextExecution' is undefined)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:63:46)
(fail) Scheduler System > CronParser > Cron Expression Parsing > should handle timezone considerations [0.07ms]
68 |         describe('Special Cron Expressions', () => {
69 |             it('should handle @yearly, @monthly, @weekly, @daily shortcuts', () => {
70 |                 const shortcuts = ['@yearly', '@monthly', '@weekly', '@daily', '@hourly'];
71 | 
72 |                 shortcuts.forEach(shortcut => {
73 |                     expect(() => parser.parse(shortcut)).not.toThrow();
                                                                  ^
error: expect(received).not.toThrow()

Error name: "TypeError"
Error message: "parser.parse is not a function. (In 'parser.parse(shortcut)', 'parser.parse' is undefined)"

      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:73:62)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:72:27)
(fail) Scheduler System > CronParser > Special Cron Expressions > should handle @yearly, @monthly, @weekly, @daily shortcuts [0.11ms]
73 |                     expect(() => parser.parse(shortcut)).not.toThrow();
74 |                 });
75 |             });
76 | 
77 |             it('should convert shortcuts to standard cron format', () => {
78 |                 expect(parser.parse('@daily')).toBe('0 0 * * *');
                                   ^
TypeError: parser.parse is not a function. (In 'parser.parse("@daily")', 'parser.parse' is undefined)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:78:31)
(fail) Scheduler System > CronParser > Special Cron Expressions > should convert shortcuts to standard cron format [0.06ms]
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:169:52)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:161:71)
(fail) Scheduler System > TaskScheduler > Schedule Creation > should create a new schedule for a task [11.85ms]
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:189:52)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:182:85)
(fail) Scheduler System > TaskScheduler > Schedule Creation > should set next execution time when creating schedule [8.95ms]
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:209:52)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:196:62)
(fail) Scheduler System > TaskScheduler > Schedule Creation > should handle schedule options [0.76ms]
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:228:46)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:221:36)
(fail) Scheduler System > TaskScheduler > Schedule Management > should enable and disable schedules [112826.95ms]
(fail) Scheduler System > TaskScheduler > Schedule Management > should enable and disable schedules
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:228:46)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:221:36)
(fail) Scheduler System > TaskScheduler > Schedule Management > should delete schedules [112832.86ms]
(fail) Scheduler System > TaskScheduler > Schedule Management > should delete schedules
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:228:46)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:221:36)
(fail) Scheduler System > TaskScheduler > Schedule Management > should update schedule cron expression [112838.75ms]
(fail) Scheduler System > TaskScheduler > X  pdate schedule cron expression
266 |                 db.run(`
267 |                     INSERT INTO task_schedules (task_id, cron_expression, next_execution, enabled)
268 |                     VALUES (1, '* * * * *', ?, 1)
269 |                 `, [pastTime]);
270 | 
271 |                 const dueSchedules = scheduler.getDueSchedules();
                                                     ^
TypeError: scheduler.getDueSchedules is not a function. (In 'scheduler.getDueSchedules()', 'scheduler.getDueSchedules' is undefined)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:271:48)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:258:61)
(fail) Scheduler System > TaskScheduler > Schedule Execution > should identify due schedules [0.35ms]
89 |         if (!nextRun) {
90 |             throw new Error('Could not calculate next execution time for cron expression');
91 |         }
92 | 
93 |         // Insert schedule
94 |         const insertStmt = this.db.prepare(`
                                        ^
SQLiteError: table task_schedules has no column named template_task_id
      errno: 1,
 byteOffset: -1,

      at prepare (bun:sqlite:307:37)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:94:36)
      at createSchedule (/workspace/banana-bun/src/scheduler/task-scheduler.ts:70:26)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:283:52)
      at <anonymous> (/workspace/banana-bun/test/scheduler.test.ts:276:62)
(fail) Scheduler System > TaskScheduler > Schedule Execution > should handle overlap policies [3.75ms]
(pass) Scheduler System > TaskScheduler > Scheduler Lifecycle > should start and stop scheduler [0.48ms]
(pass) Scheduler System > TaskScheduler > Scheduler Lifecycle > should handle multiple start/stop calls [0.07ms]

test/autolearn-agent.test.ts:
(pass) AutolearnAgent > Constructor > should initialize with default enhanced learning service configuration [0.04ms]
(pass) AutolearnAgent > Constructor > should set correct default thresholds [0.06ms]
(pass) AutolearnAgent > generateLearningInsights > should generate performance insights from task data [1.36ms]
(pass) AutolearnAgent > generateLearningInsights > should provide actionable recommendations [0.28ms]
(pass) AutolearnAgent > generateOptimizationRecommendations > should generate task scheduling recommendations [0.56ms]
(pass) AutolearnAgent > generateOptimizationRecommendations > should prioritize recommendations by impact [0.16ms]
(pass) AutolearnAgent > Learning Cycle Integration > should complete full learning cycle from task execution to recommendations [0.76ms]

test/analytics-logger.test.ts:
45 | 
46 | describe('AnalyticsLogger basic operations', () => {
47 |   it('logs task start', async () => {
48 |     db.run("INSERT INTO tasks (id, type, status) VALUES (1, 'llm', 'pending')");
49 |     const task = { id: 1, type: 'llm', description: 'Test', status: 'pending' };
50 |     await analyticsLogger.logTaskStart(task);
                               ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(task)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:50:27)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:47:25)
(fail) AnalyticsLogger basic operations > logs task start [0.26ms]
62 |   });
63 | 
64 |   it('logs task completion', async () => {
65 |     db.run("INSERT INTO tasks (id, type, status) VALUES (2, 'llm', 'pending')");
66 |     const task = { id: 2, type: 'llm', description: 'Complete', status: 'pending' };
67 |     await analyticsLogger.logTaskStart(task);
                               ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(task)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:67:27)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:64:30)
(fail) AnalyticsLogger basic operations > logs task completion [0.30ms]
81 |   });
82 | 
83 |   it('logs task error and retry', async () => {
84 |     db.run("INSERT INTO tasks (id, type, status) VALUES (3, 'shell', 'pending')");
85 |     const task = { id: 3, type: 'shell', description: 'Err', status: 'pending' };
86 |     await analyticsLogger.logTaskStart(task);
                               ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(task)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:86:27)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:83:35)
(fail) AnalyticsLogger basic operations > logs task error and retry [0.24ms]
105 | describe('AnalyticsLogger analytics functions', () => {
106 |   it('computes task analytics', async () => {
107 |     for (let i = 1; i <= 3; i++) {
108 |       db.run(`INSERT INTO tasks (id, type, status) VALUES (${i + 10}, 'shell', 'pending')`);
109 |       const t = { id: i + 10, type: 'shell', description: 'ok', status: 'pending' };
110 |       await analyticsLogger.logTaskStart(t);
                                  ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(t)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:110:29)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:106:33)
(fail) AnalyticsLogger analytics functions > computes task analytics [0.33ms]
128 | 
129 |   it('detects bottlenecks', async () => {
130 |     for (let i = 1; i <= 3; i++) {
131 |       db.run(`INSERT INTO tasks (id, type, status) VALUES (${30 + i}, 'shell', 'pending')`);
132 |       const t = { id: 30 + i, type: 'shell', description: 'slow', status: 'pending' };
133 |       await analyticsLogger.logTaskStart(t);
                                  ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(t)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:133:29)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:129:29)
(fail) AnalyticsLogger analytics functions > detects bottlenecks [0.25ms]
142 |   });
143 | 
144 |   it('cleans old logs', async () => {
145 |     db.run("INSERT INTO tasks (id, type, status) VALUES (99, 'shell', 'pending')");
146 |     db.run(`INSERT INTO task_logs (task_id, task_type, status, duration_ms, retries, created_at) VALUES (99, 'shell', 'completed', 10, 0, datetime('now','-40 day'))`);
147 |     const deleted = await analyticsLogger.cleanupOldLogs(30);
                                                ^
TypeError: analyticsLogger.cleanupOldLogs is not a function. (In 'analyticsLogger.cleanupOldLogs(30)', 'analyticsLogger.cleanupOldLogs' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:147:43)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:144:25)
(fail) AnalyticsLogger analytics functions > cleans old logs [0.30ms]
155 |   it('handles database errors gracefully', async () => {
156 |     mockGetDatabase.mockImplementationOnce(() => {
157 |       throw new Error('fail');
158 |     });
159 |     const task = { id: 50, type: 'llm', description: 'x', status: 'pending' };
160 |     await expect(analyticsLogger.logTaskStart(task)).resolves.toBeUndefined();
                                       ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(task)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:160:34)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:155:44)
(fail) AnalyticsLogger edge cases > handles database errors gracefully [0.18ms]
162 | 
163 |   it('recommends optimization for frequent slow tasks', async () => {
164 |     for (let i = 1; i <= 11; i++) {
165 |       db.run(`INSERT INTO tasks (id, type, status) VALUES (${200 + i}, 'llm', 'pending')`);
166 |       const t = { id: 200 + i, type: 'llm', description: 'slow', status: 'pending' };
167 |       await analyticsLogger.logTaskStart(t);
                                  ^
TypeError: analyticsLogger.logTaskStart is not a function. (In 'analyticsLogger.logTaskStart(t)', 'analyticsLogger.logTaskStart' is undefined)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:167:29)
      at <anonymous> (/workspace/banana-bun/test/analytics-logger.test.ts:163:57)
(fail) AnalyticsLogger edge cases > recommends optimization for frequent slow tasks [0.25ms]

test/mcp-manager.test.ts:

# Unhandled error between tests
-------------------------------
-------------------------------


test/planner-service.db.test.ts:
(pass) PlannerService Database Functions > returns latest planner result for a task [0.16ms]
(pass) PlannerService Database Functions > calculates planner metrics correctly [0.46ms]
(pass) PlannerService Database Functions > retrieves task planner history [0.13ms]
(pass) PlannerService Database Functions > retrieves recent planner results [0.13ms]

test/planner-service-badge.test.ts:
(pass) PlannerService Badge Helpers > getContextUsageBadge > handles empty or null values [0.09ms]
(pass) PlannerService Badge Helpers > getContextUsageBadge > renders counts correctly [0.82ms]
(pass) PlannerService Badge Helpers > getContextUsageBadge > handles invalid JSON input [0.13ms]
(pass) PlannerService Badge Helpers > getSubtaskCountBadge > returns color based on subtask count [0.08ms]

test/mcp-client.test.ts:
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Connection Management > should connect to MCP server [112877.87ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Connection Management > should connect to MCP server
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Connection Management > should handle connection errors [112878.06ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Connection Management > should handle connection errors
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Connection Management > should disconnect gracefully [112878.12ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Connection Management > should disconnect gracefully
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Connection Management > should check connection status [112878.16ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Connection Management > should check connection status
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Connection Management > should handle reconnection [112878.18ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > @X  andle reconnection
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Tool Execution > should call tool with parameters [112878.22ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Tool Execution > should call tool with parameters
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Tool Execution > should handle tool execution errors [112878.25ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Tool Execution > should handle tool execution errors
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Tool Execution > should handle timeout for tool calls [112878.28ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Tool Execution > should handle timeout for tool calls
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Tool Execution > should handle malformed responses [112878.31ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > 0X  andle malformed responses
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Message Handling > should handle server notifications [112878.35ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Message Handling > should handle server notifications
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Message Handling > should handle ping/pong for keepalive [112878.38ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Message Handling > should handle ping/pong for keepalive
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Message Handling > should queue messages when disconnected [112878.41ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > X  ueue messages when disconnected
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Error Recovery > should handle connection drops [112878.43ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Error Recovery > should handle connection drops
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Error Recovery > should handle network errors [112878.46ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Error Recovery > should handle network errors
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Error Recovery > should retry failed connections [112878.51ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > X  etry failed connections
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Resource Management > should clean up resources on disconnect [112878.54ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Resource Management > should clean up resources on disconnect
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Resource Management > should handle multiple disconnect calls [112878.58ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > Resource Management > should handle multiple disconnect calls
40 | describe('MCP Client', () => {
41 |     let mcpClient: MCPClient;
42 |     const testServerUrl = 'ws://localhost:8080';
43 | 
44 |     beforeEach(() => {
45 |         mcpClient = new McpClient(testServerUrl);
                             ^
ReferenceError: McpClient is not defined
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:45:25)
(fail) MCP Client > Resource Management > should clear pending requests on disconnect [112878.61ms]

# Unhandled error between tests
-------------------------------
56 |             }
57 |         });
58 |     });
59 | 
60 |     afterEach(async () => {
61 |         await mcpClient.disconnect();
                   ^
TypeError: undefined is not an object (evaluating 'mcpClient.disconnect')
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:61:15)
      at <anonymous> (/workspace/banana-bun/test/mcp-client.test.ts:60:15)
-------------------------------

(fail) MCP Client > @X  lear pending requests on disconnect

test/enhanced-task-processor.test.ts:
(pass) Enhanced Task Processor > Initialization > should initialize successfully [4.08ms]
(pass) Enhanced Task Processor > Initialization > should handle initialization errors gracefully [0.19ms]
118 |                     similarity: 0.8,
119 |                     created_at: new Date().toISOString()
120 |                 }
121 |             ]);
122 | 
123 |             const result = await enhancedTaskProcessor.processTaskWithLearning(task);
                                                             ^
TypeError: enhancedTaskProcessor.processTaskWithLearning is not a function. (In 'enhancedTaskProcessor.processTaskWithLearning(task)', 'enhancedTaskProcessor.processTaskWithLearning' is undefined)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:123:56)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:103:62)
(fail) Enhanced Task Processor > Task Processing > should process task with learning capabilities [0.37ms]
139 |                 status: 'pending'
140 |             };
141 | 
142 |             mockEmbeddingManager.findSimilarTasks.mockResolvedValueOnce([]);
143 | 
144 |             const result = await enhancedTaskProcessor.processTaskWithLearning(task);
                                                             ^
TypeError: enhancedTaskProcessor.processTaskWithLearning is not a function. (In 'enhancedTaskProcessor.processTaskWithLearning(task)', 'enhancedTaskProcessor.processTaskWithLearning' is undefined)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:144:56)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:134:59)
(fail) Enhanced Task Processor > Task Processing > should handle tasks without similar matches [0.24ms]
155 |                 description: 'Run system command',
156 |                 status: 'pending',
157 |                 shell_command: 'ls -la'
158 |             };
159 | 
160 |             const result = await enhancedTaskProcessor.processTaskWithLearning(shellTask);
                                                             ^
TypeError: enhancedTaskProcessor.processTaskWithLearning is not a function. (In 'enhancedTaskProcessor.processTaskWithLearning(shellTask)', 'enhancedTaskProcessor.processTaskWithLearning' is undefined)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:160:56)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:151:66)
(fail) Enhanced Task Processor > Task Processing > should generate recommendations based on task type [0.22ms]
180 |             const result = {
181 |                 success: true,
182 |                 outputPath: '/test/output.py'
183 |             };
184 | 
185 |             await enhancedTaskProcessor.completeTaskWithLearning(task, result);
                                              ^
TypeError: enhancedTaskProcessor.completeTaskWithLearning is not a function. (In 'enhancedTaskProcessor.completeTaskWithLearning(task, result)', 'enhancedTaskProcessor.completeTaskWithLearning' is undefined)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:185:41)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:172:57)
(fail) Enhanced Task Processor > Task Completion with Learning > should complete task and store embeddings [0.28ms]
209 |             };
210 | 
211 |             mockEmbeddingManager.addTaskEmbedding.mockRejectedValueOnce(new Error('Embedding failed'));
212 | 
213 |             // Should not throw, but log error
214 |             await enhancedTaskProcessor.completeTaskWithLearning(task, result);
                                              ^
TypeError: enhancedTaskProcessor.completeTaskWithLearning is not a function. (In 'enhancedTaskProcessor.completeTaskWithLearning(task, result)', 'enhancedTaskProcessor.completeTaskWithLearning' is undefined)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:214:41)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:198:58)
(fail) Enhanced Task Processor > Task Completion with Learning > should handle completion errors gracefully [0.25ms]

# Unhandled error between tests
-------------------------------
206 |             const result = {
207 |                 success: false,
208 |                 error: 'Command failed'
209 |             };
210 | 
211 |             mockEmbeddingManager.addTaskEmbedding.mockRejectedValueOnce(new Error('Embedding failed'));
                                                                              ^
error: Embedding failed
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:211:73)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:198:58)
-------------------------------


# Unhandled error between tests
-------------------------------
209 |             };
210 | 
211 |             mockEmbeddingManager.addTaskEmbedding.mockRejectedValueOnce(new Error('Embedding failed'));
212 | 
213 |             // Should not throw, but log error
214 |             await enhancedTaskProcessor.completeTaskWithLearning(task, result);
                                              ^
TypeError: enhancedTaskProcessor.completeTaskWithLearning is not a function. (In 'enhancedTaskProcessor.completeTaskWithLearning(task, result)', 'enhancedTaskProcessor.completeTaskWithLearning' is undefined)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:214:41)
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:198:58)
-------------------------------

225 |             const dashboardInfo = enhancedTaskProcessor.getLiveDashboardInfo();
226 | 
227 |             expect(dashboardInfo).toHaveProperty('websocketUrl');
228 |             expect(dashboardInfo).toHaveProperty('dashboardPath');
229 |             expect(dashboardInfo.websocketUrl).toContain('ws://');
230 |             expect(dashboardInfo.websocketUrl).toContain('8080');
                                                     ^
error: expect(received).toContain(expected)

Expected to contain: "8080"
Received: "ws://localhost:8081"

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:230:48)
(fail) Enhanced Task Processor > Live Dashboard Integration > should provide dashboard info [0.21ms]
(pass) Enhanced Task Processor > Live Dashboard Integration > should handle WebSocket connections [1.37ms]
254 |         });
255 | 
256 |         it('should calculate system metrics', async () => {
257 |             const metrics = await enhancedTaskProcessor.getSystemMetrics(24);
258 | 
259 |             expect(metrics).toHaveProperty('total_tasks');
                                  ^
error: expect(received).toHaveProperty(path)

Expected path: "total_tasks"

Unable to find property

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:259:29)
(fail) Enhanced Task Processor > System Metrics > should calculate system metrics [0.71ms]
269 | 
270 |         it('should calculate success rate correctly', async () => {
271 |             const metrics = await enhancedTaskProcessor.getSystemMetrics(24);
272 | 
273 |             // 1 completed out of 2 finished tasks (excluding pending)
274 |             expect(metrics.success_rate).toBe(0.5);
                                               ^
error: expect(received).toBe(expected)

Expected: 0.5
Received: undefined

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:274:42)
(fail) Enhanced Task Processor > System Metrics > should calculate success rate correctly [0.37ms]
275 |         });
276 | 
277 |         it('should determine system health status', async () => {
278 |             const metrics = await enhancedTaskProcessor.getSystemMetrics(24);
279 | 
280 |             expect(['healthy', 'warning', 'critical']).toContain(metrics.system_health);
                                                             ^
error: expect(received).toContain(expected)

Expected to contain: {
  pending_tasks: 1,
  running_tasks: 0,
  failed_tasks: 1,
  websocket_connections: 0,
}
Received: [ "healthy", "warning", "critical" ]

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:280:56)
(fail) Enhanced Task Processor > System Metrics > should determine system health status [0.36ms]
284 |     describe('Shutdown', () => {
285 |         it('should shutdown gracefully', async () => {
286 |             await enhancedTaskProcessor.initialize();
287 |             await enhancedTaskProcessor.shutdown();
288 | 
289 |             expect(mockEmbeddingManager.shutdown).toHaveBeenCalled();
                                                        ^
error: expect(received).toHaveBeenCalled()

Expected number of calls: >= 1
Received number of calls: 0

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:289:51)
(fail) Enhanced Task Processor > Shutdown > should shutdown gracefully [1.54ms]
298 |             mockEmbeddingManager.shutdown.mockRejectedValueOnce(new Error('Shutdown failed'));
299 | 
300 |             // Should not throw
301 |             await enhancedTaskProcessor.shutdown();
302 | 
303 |             expect(mockLogger.error).toHaveBeenCalledWith(
                                           ^
error: expect(received).toHaveBeenCalledWith(expected)

Number of calls: 0

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:303:38)
(fail) Enhanced Task Processor > Shutdown > should handle shutdown errors [1.56ms]

# Unhandled error between tests
-------------------------------
293 |         });
294 | 
295 |         it('should handle shutdown errors', async () => {
296 |             await enhancedTaskProcessor.initialize();
297 | 
298 |             mockEmbeddingManager.shutdown.mockRejectedValueOnce(new Error('Shutdown failed'));
                                                                      ^
error: Shutdown failed
      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:298:65)
-------------------------------


# Unhandled error between tests
-------------------------------
298 |             mockEmbeddingManager.shutdown.mockRejectedValueOnce(new Error('Shutdown failed'));
299 | 
300 |             // Should not throw
301 |             await enhancedTaskProcessor.shutdown();
302 | 
303 |             expect(mockLogger.error).toHaveBeenCalledWith(
                                           ^
error: expect(received).toHaveBeenCalledWith(expected)

Number of calls: 0

      at <anonymous> (/workspace/banana-bun/test/enhanced-task-processor.test.ts:303:38)
-------------------------------


test/main-orchestrator.test.ts:
(fail) Main Orchestrator (src/index.ts) > System Initialization > should initialize all components in correct order [4.31ms]
(pass) Main Orchestrator (src/index.ts) > System Initialization > should create required directories [0.67ms]
183 |                 expect(exists).toBe(true);
184 |             }
185 |         });
186 | 
187 |         it('should handle initialization errors gracefully', async () => {
188 |             mockInitDatabase.mockRejectedValueOnce(new Error('Database init failed'));
                                                         ^
error: Database init failed
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:188:52)
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:187:62)
(fail) Main Orchestrator (src/index.ts) > System Initialization > should handle initialization errors gracefully [0.23ms]
(pass) Main Orchestrator (src/index.ts) > File Processing > should process new task files [2.58ms]
(pass) Main Orchestrator (src/index.ts) > File Processing > should handle malformed task files [0.29ms]

# Unhandled error between tests
-------------------------------
221 | 
222 |         it('should handle malformed task files', async () => {
223 |             const testFile = `${mockConfig.paths.incoming}/bad-task.json`;
224 |             await fs.writeFile(testFile, 'invalid json content');
225 | 
226 |             mockParseTaskFile.mockRejectedValueOnce(new Error('Invalid JSON'));
                                                          ^
error: Invalid JSON
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:226:53)
-------------------------------

(pass) Main Orchestrator (src/index.ts) > File Processing > should move processed files to archive [0.23ms]
(pass) Main Orchestrator (src/index.ts) > Task Orchestration > should process pending tasks [0.37ms]
(pass) Main Orchestrator (src/index.ts) > Task Orchestration > should handle task dependencies [0.25ms]
(pass) Main Orchestrator (src/index.ts) > Task Orchestration > should retry failed tasks [0.12ms]
(pass) Main Orchestrator (src/index.ts) > Dashboard Generation > should generate dashboard periodically [0.07ms]
293 |             // Test dashboard generation
294 |             expect(mockGenerateDashboard).toBeDefined();
295 |         });
296 | 
297 |         it('should handle dashboard generation errors', async () => {
298 |             mockGenerateDashboard.mockRejectedValueOnce(new Error('Dashboard error'));
                                                              ^
error: Dashboard error
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:298:57)
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:297:57)
(fail) Main Orchestrator (src/index.ts) > Dashboard Generation > should handle dashboard generation errors [0.15ms]
(pass) Main Orchestrator (src/index.ts) > MCP Integration > should initialize MCP enhanced task processor [0.10ms]
306 |         it('should initialize MCP enhanced task processor', async () => {
307 |             expect(mockEnhancedTaskProcessor.initialize).toBeDefined();
308 |         });
309 | 
310 |         it('should handle MCP initialization failures', async () => {
311 |             mockEnhancedTaskProcessor.initialize.mockRejectedValueOnce(new Error('MCP init failed'));
                                                                             ^
error: MCP init failed
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:311:72)
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:310:57)
(fail) Main Orchestrator (src/index.ts) > MCP Integration > should handle MCP initialization failures [0.15ms]
(pass) Main Orchestrator (src/index.ts) > MCP Integration > should provide live dashboard info [0.07ms]
321 |         });
322 |     });
323 | 
324 |     describe('Error Handling', () => {
325 |         it('should handle database connection errors', async () => {
326 |             mockInitDatabase.mockRejectedValueOnce(new Error('Database connection failed'));
                                                         ^
error: Database connection failed
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:326:52)
      at <anonymous> (/workspace/banana-bun/test/main-orchestrator.test.ts:325:56)
(fail) Main Orchestrator (src/index.ts) > Error Handling > should handle database connection errors [1.04ms]
(pass) Main Orchestrator (src/index.ts) > Error Handling > should handle file system errors [0.27ms]
(pass) Main Orchestrator (src/index.ts) > Graceful Shutdown > should handle SIGINT signal [0.25ms]
(pass) Main Orchestrator (src/index.ts) > Graceful Shutdown > should handle SIGTERM signal [0.08ms]

test/validation.test.ts:
12 |                     type: 'shell',
13 |                     description: 'Test shell task',
14 |                     status: 'pending'
15 |                 };
16 | 
17 |                 expect(() => validateTaskSchema(validTask)).not.toThrow();
                                                                     ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: shell_command is required and must be a non-empty string"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:17:65)
(fail) Validation System > Task Schema Validation > Base Task Validation > should validate valid base task [0.74ms]
(pass) Validation System > Task Schema Validation > Base Task Validation > should reject task without required fields [0.16ms]
39 |                         id: 1,
40 |                         type: 'shell',
41 |                         description: 'Test task',
42 |                         status: status as any
43 |                     };
44 |                     expect(() => validateTaskSchema(task)).not.toThrow();
                                                                    ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: shell_command is required and must be a non-empty string"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:44:64)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:37:31)
(fail) Validation System > Task Schema Validation > Base Task Validation > should validate task status values [0.17ms]
64 |                         id: 1,
65 |                         type: type as any,
66 |                         description: 'Test task',
67 |                         status: 'pending'
68 |                     };
69 |                     expect(() => validateTaskSchema(task)).not.toThrow();
                                                                    ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: shell_command is required and must be a non-empty string"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:69:64)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:62:28)
(fail) Validation System > Task Schema Validation > Base Task Validation > should validate task types [0.14ms]
(pass) Validation System > Task Schema Validation > Shell Task Validation > should validate valid shell task [0.07ms]
(pass) Validation System > Task Schema Validation > Shell Task Validation > should require shell_command for shell tasks [0.08ms]
(pass) Validation System > Task Schema Validation > Shell Task Validation > should reject empty shell commands [0.09ms]
(pass) Validation System > Task Schema Validation > LLM Task Validation > should validate valid LLM task [0.11ms]
(pass) Validation System > Task Schema Validation > LLM Task Validation > should validate LLM task with optional fields [0.06ms]
167 |                         type: 'llm',
168 |                         description: 'Test task',
169 |                         status: 'pending',
170 |                         temperature: temp
171 |                     };
172 |                     expect(() => validateTaskSchema(task)).toThrow();
                                                                 ^
error: expect(received).toThrow()

Received function did not throw
Received value: undefined

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:172:60)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:164:37)
(fail) Validation System > Task Schema Validation > LLM Task Validation > should validate temperature range [0.25ms]
(pass) Validation System > Task Schema Validation > Code Task Validation > should validate valid code task [0.16ms]
(pass) Validation System > Task Schema Validation > Code Task Validation > should validate code task with language specification [0.09ms]
(pass) Validation System > Task Schema Validation > Code Task Validation > should validate supported programming languages [0.17ms]
223 |                     description: 'Task with dependencies',
224 |                     status: 'pending',
225 |                     dependencies: ['1', '2', '3']
226 |                 };
227 | 
228 |                 expect(() => validateTaskSchema(taskWithDeps)).not.toThrow();
                                                                         ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: shell_command is required and must be a non-empty string"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:228:68)
(fail) Validation System > Task Schema Validation > Dependencies Validation > should validate task dependencies format [0.16ms]
(pass) Validation System > Task Schema Validation > Dependencies Validation > should reject invalid dependency formats [0.15ms]
257 |                 description: 'Test shell task',
258 |                 status: 'pending',
259 |                 shell_command: 'echo "test"'
260 |             });
261 | 
262 |             expect(() => validateTaskFile(validJsonContent, 'json')).not.toThrow();
                                                                               ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: id must be a string or number"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:262:74)
(fail) Validation System > Task File Validation > should validate JSON task file content [0.21ms]
268 | description: Generate a story
269 | status: pending
270 | context: Write a short story about AI
271 | `;
272 | 
273 |             expect(() => validateTaskFile(validYamlContent, 'yaml')).not.toThrow();
                                                                               ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Failed to parse YAML content: JSON Parse error: Unexpected identifier \"type\""

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:273:74)
(fail) Validation System > Task File Validation > should validate YAML task file content [0.14ms]
(pass) Validation System > Task File Validation > should reject malformed JSON [0.06ms]
(pass) Validation System > Task File Validation > should reject malformed YAML [0.04ms]
(pass) Validation System > Task File Validation > should validate task content after parsing [0.06ms]
(pass) Validation System > Batch Validation > should validate multiple tasks at once [0.13ms]
(pass) Validation System > Batch Validation > should identify invalid tasks in batch [0.15ms]
372 |                 status: 'pending'
373 |             };
374 | 
375 |             expect(() => validateTaskSchema(taskWithShortDesc)).toThrow();
376 |             expect(() => validateTaskSchema(taskWithLongDesc)).toThrow();
377 |             expect(() => validateTaskSchema(taskWithValidDesc)).not.toThrow();
                                                                          ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: shell_command is required and must be a non-empty string"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:377:69)
(fail) Validation System > Custom Validation Rules > should validate task description length [0.34ms]
386 |                     id,
387 |                     type: 'shell',
388 |                     description: 'Test task',
389 |                     status: 'pending'
390 |                 };
391 |                 expect(() => validateTaskSchema(task)).not.toThrow();
                                                                 ^
error: expect(received).not.toThrow()

Error name: "Error"
Error message: "Task validation failed: shell_command is required and must be a non-empty string"

      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:391:60)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/validation.test.ts:384:22)
(fail) Validation System > Custom Validation Rules > should validate task ID format [0.25ms]

test/phase2-summarization.test.ts:

# Unhandled error between tests
-------------------------------
error: Cannot find package 'openai' from '/workspace/banana-bun/src/services/summarizer.ts'
-------------------------------


test/smart-transcribe.test.ts:
(pass) Smart Transcribe CLI Parsing > parses file path parameter [0.19ms]
(pass) Smart Transcribe CLI Parsing > parses quality parameter with valid values [0.05ms]
(pass) Smart Transcribe CLI Parsing > rejects invalid quality values [0.07ms]
(pass) Smart Transcribe CLI Parsing > parses batch directory parameter [0.05ms]
(pass) Smart Transcribe CLI Parsing > parses feedback parameters [0.05ms]
(pass) Smart Transcribe CLI Parsing > validates rating range [0.12ms]
(pass) File Validation > validates audio file exists [0.62ms]
(pass) File Validation > rejects non-existent files [0.31ms]
(pass) File Validation > rejects unsupported file formats [0.33ms]
(pass) File Validation > finds audio files in directory [2.38ms]
(pass) Helper functions > formats time [0.16ms]
(pass) Helper functions > formats duration [0.07ms]

test/dashboard.test.ts:
293 |             // Test dashboard generation
294 |             expect(mockGenerateDashboard).toBeDefined();
295 |         });
296 | 
297 |         it('should handle dashboard generation errors', async () => {
298 |             mockGenerateDashboard.mockRejectedValueOnce(new Error('Dashboard error'));
                                                                  ^
error: Dashboard error
      at /workspace/banana-bun/test/main-orchestrator.test.ts:298:61
(fail) Dashboard Generation > Dashboard HTML Generation > should generate dashboard HTML file [0.32ms]
ENOENT: no such file or directory, open '/tmp/test-dashboard/index.html'
    path: "/tmp/test-dashboard/index.html",
 syscall: "open",
   errno: -2,
    code: "ENOENT"

(fail) Dashboard Generation > Dashboard HTML Generation > should include task statistics in dashboard [0.50ms]
129 |         it('should handle empty task list', async () => {
130 |             await generateDashboard();
131 | 
132 |             const dashboardPath = `${dashboardDir}/index.html`;
133 |             const dashboardExists = await fs.access(dashboardPath).then(() => true).catch(() => false);
134 |             expect(dashboardExists).toBe(true);
                                          ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/dashboard.test.ts:134:37)
(fail) Dashboard Generation > Dashboard HTML Generation > should handle empty task list [5.41ms]
ENOENT: no such file or directory, open '/tmp/test-dashboard/index.html'
    path: "/tmp/test-dashboard/index.html",
 syscall: "open",
   errno: -2,
    code: "ENOENT"

(fail) Dashboard Generation > Dashboard HTML Generation > should include task type breakdown [0.55ms]
168 |     describe('Error Handling', () => {
169 |         it('should handle database errors gracefully', async () => {
170 |             // Close database to simulate error
171 |             db.close();
172 | 
173 |             await expect(generateDashboard()).rejects.toThrow();
                                                            ^
error: 

Expected promise that rejects
Received promise that resolved: Promise { <resolved> }

      at <anonymous> (/workspace/banana-bun/test/dashboard.test.ts:173:55)
      at <anonymous> (/workspace/banana-bun/test/dashboard.test.ts:169:56)
(fail) Dashboard Generation > Error Handling > should handle database errors gracefully [0.20ms]
177 |             // Mock fs.writeFile to throw an error
178 |             const originalWriteFile = fs.writeFile;
179 |             (fs as any).writeFile = mock(() => Promise.reject(new Error('File system error')));
180 | 
181 |             try {
182 |                 await expect(generateDashboard()).rejects.toThrow();
                                                                ^
error: 

Expected promise that rejects
Received promise that resolved: Promise { <resolved> }

      at <anonymous> (/workspace/banana-bun/test/dashboard.test.ts:182:59)
      at <anonymous> (/workspace/banana-bun/test/dashboard.test.ts:176:59)
(fail) Dashboard Generation > Error Handling > should handle file system errors gracefully [0.24ms]
ENOENT: no such file or directory, open '/tmp/test-dashboard/index.html'
    path: "/tmp/test-dashboard/index.html",
 syscall: "open",
   errno: -2,
    code: "ENOENT"

(fail) Dashboard Generation > Dashboard Content Validation > should generate valid HTML structure [0.34ms]
ENOENT: no such file or directory, open '/tmp/test-dashboard/index.html'
    path: "/tmp/test-dashboard/index.html",
 syscall: "open",
   errno: -2,
    code: "ENOENT"

(fail) Dashboard Generation > Dashboard Content Validation > should escape HTML in task descriptions [0.34ms]

test/enhanced-learning-service.test.ts:

# Unhandled error between tests
-------------------------------
94 |             enable_cross_modal_analysis: false, // Disable for testing
95 |             enable_temporal_analysis: true
96 |         });
97 | 
98 |         // Mock the database getter
99 |         const originalGetDatabase = require('../src/db').getDatabase;
                                                              ^
TypeError: Attempted to assign to readonly property.
      at <anonymous> (/workspace/banana-bun/test/enhanced-learning-service.test.ts:99:58)
      at <anonymous> (/workspace/banana-bun/test/enhanced-learning-service.test.ts:10:15)
      at /workspace/banana-bun/test/enhanced-learning-service.test.ts:6:1
      at loadAndEvaluateModule (2:1)
-------------------------------

(fail) Enhanced Learning Service > should generate enhanced learning rules from feedback patterns
(fail) Enhanced Learning Service > should store enhanced learning rules in database
(fail) Enhanced Learning Service > should apply rules automatically to media
(fail) Enhanced Learning Service > should calculate strategy performance
(fail) Enhanced Learning Service > should filter and rank rules by quality
(fail) Enhanced Learning Service > should handle cross-modal analysis when enabled

test/utils.test.ts:
25 |             await fs.writeFile(testFile, content);
26 | 
27 |             const hash1 = await hashFile(testFile);
28 |             const hash2 = await hashFile(testFile);
29 | 
30 |             expect(hash1).toBe(hash2);
                               ^
error: expect(received).toBe(expected)

Expected: "test-hash"
Received: "test-file-hash"

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:30:27)
(fail) Utility Functions > Hash Utility > should generate consistent hash for same file content [0.46ms]
40 |             await fs.writeFile(testFile2, 'Content 2');
41 | 
42 |             const hash1 = await hashFile(testFile1);
43 |             const hash2 = await hashFile(testFile2);
44 | 
45 |             expect(hash1).not.toBe(hash2);
                                   ^
error: expect(received).not.toBe(expected)

Expected: not "test-hash"

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:45:31)
(fail) Utility Functions > Hash Utility > should generate different hashes for different content [0.40ms]
(pass) Utility Functions > Hash Utility > should handle empty files [0.29ms]
72 |             await fs.writeFile(taskFile, taskContent);
73 | 
74 |             const task = await parseTaskFile(taskFile);
75 | 
76 |             expect(task.type).toBe('shell');
77 |             expect(task.shell_command).toBe('echo "Hello World"');
                                            ^
error: expect(received).toBe(expected)

Expected: "echo \"Hello World\""
Received: "echo \"hello\""

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:77:40)
(fail) Utility Functions > Task Parser > should parse YAML task file [0.30ms]
221 | 
222 |         it('should handle malformed task files', async () => {
223 |             const testFile = `${mockConfig.paths.incoming}/bad-task.json`;
224 |             await fs.writeFile(testFile, 'invalid json content');
225 | 
226 |             mockParseTaskFile.mockRejectedValueOnce(new Error('Invalid JSON'));
                                                              ^
error: Invalid JSON
      at /workspace/banana-bun/test/main-orchestrator.test.ts:226:57
(fail) Utility Functions > Task Parser > should parse JSON task file [0.34ms]
110 | 
111 |             await fs.writeFile(taskFile, taskContent);
112 | 
113 |             const task = await parseTaskFile(taskFile);
114 | 
115 |             expect(task.type).toBe('code');
                                    ^
error: expect(received).toBe(expected)

Expected: "code"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:115:31)
(fail) Utility Functions > Task Parser > should parse Markdown task file with frontmatter [0.33ms]
130 | 
131 |             await fs.writeFile(taskFile, taskContent);
132 | 
133 |             const task = await parseTaskFile(taskFile);
134 | 
135 |             expect(task.type).toBe('tool');
                                    ^
error: expect(received).toBe(expected)

Expected: "tool"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:135:31)
(fail) Utility Functions > Task Parser > should handle tool task with args [0.34ms]
151 | 
152 |             await fs.writeFile(taskFile, taskContent);
153 | 
154 |             const task = await parseTaskFile(taskFile);
155 | 
156 |             expect(task.type).toBe('batch');
                                    ^
error: expect(received).toBe(expected)

Expected: "batch"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:156:31)
(fail) Utility Functions > Task Parser > should handle batch task with generator [0.30ms]
172 | 
173 |             await fs.writeFile(taskFile, taskContent);
174 | 
175 |             const task = await parseTaskFile(taskFile);
176 | 
177 |             expect(task.type).toBe('review');
                                    ^
error: expect(received).toBe(expected)

Expected: "review"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:177:31)
(fail) Utility Functions > Task Parser > should handle task with dependencies [0.32ms]
216 |                 }
217 |             ];
218 | 
219 |             const baseTasks = convertDatabaseTasksToBaseTasks(dbTasks);
220 | 
221 |             expect(baseTasks).toHaveLength(2);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 2
Received length: 1

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:221:31)
(fail) Utility Functions > Task Converter > should convert database tasks to base tasks [0.19ms]
253 |                 }
254 |             ];
255 | 
256 |             const baseTasks = convertDatabaseTasksToBaseTasks(dbTasks);
257 | 
258 |             expect(baseTasks).toHaveLength(1);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:258:31)
(fail) Utility Functions > Task Converter > should handle tool tasks with JSON args [0.20ms]
288 |                 }
289 |             ];
290 | 
291 |             const baseTasks = convertDatabaseTasksToBaseTasks(dbTasks);
292 | 
293 |             expect(baseTasks).toHaveLength(1);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0

      at <anonymous> (/workspace/banana-bun/test/utils.test.ts:293:31)
(fail) Utility Functions > Task Converter > should handle batch tasks with generator [0.16ms]
(pass) Utility Functions > Task Converter > should handle empty database task array [0.04ms]

test/feedback-tracker.test.ts:
66 |         db = new Database(':memory:');
67 |         createTestTables(db);
68 |         mock.module('../src/db', () => ({ getDatabase: () => db }));
69 |         const mod = await import('../src/feedback-tracker');
70 |         FeedbackTrackerClass = mod.FeedbackTracker;
71 |         tracker = new FeedbackTrackerClass();
                       ^
TypeError: undefined is not a constructor (evaluating 'new FeedbackTrackerClass')
      at <anonymous> (/workspace/banana-bun/test/feedback-tracker.test.ts:71:19)
(fail) FeedbackTracker > records user feedback [113039.43ms]
(fail) FeedbackTracker > records user feedback
66 |         db = new Database(':memory:');
67 |         createTestTables(db);
68 |         mock.module('../src/db', () => ({ getDatabase: () => db }));
69 |         const mod = await import('../src/feedback-tracker');
70 |         FeedbackTrackerClass = mod.FeedbackTracker;
71 |         tracker = new FeedbackTrackerClass();
                       ^
TypeError: undefined is not a constructor (evaluating 'new FeedbackTrackerClass')
      at <anonymous> (/workspace/banana-bun/test/feedback-tracker.test.ts:71:19)
(fail) FeedbackTracker > analyzes feedback patterns [113039.89ms]
(fail) FeedbackTracker > analyzes feedback patterns
66 |         db = new Database(':memory:');
67 |         createTestTables(db);
68 |         mock.module('../src/db', () => ({ getDatabase: () => db }));
69 |         const mod = await import('../src/feedback-tracker');
70 |         FeedbackTrackerClass = mod.FeedbackTracker;
71 |         tracker = new FeedbackTrackerClass();
                       ^
TypeError: undefined is not a constructor (evaluating 'new FeedbackTrackerClass')
      at <anonymous> (/workspace/banana-bun/test/feedback-tracker.test.ts:71:19)
(fail) FeedbackTracker > generates learning rules from patterns [113040.29ms]
(fail) FeedbackTracker > generates learning rules from patterns
66 |         db = new Database(':memory:');
67 |         createTestTables(db);
68 |         mock.module('../src/db', () => ({ getDatabase: () => db }));
69 |         const mod = await import('../src/feedback-tracker');
70 |         FeedbackTrackerClass = mod.FeedbackTracker;
71 |         tracker = new FeedbackTrackerClass();
                       ^
TypeError: undefined is not a constructor (evaluating 'new FeedbackTrackerClass')
      at <anonymous> (/workspace/banana-bun/test/feedback-tracker.test.ts:71:19)
(fail) FeedbackTracker > computes feedback statistics [113040.63ms]
(fail) FeedbackTracker > computes feedback statistics
66 |         db = new Database(':memory:');
67 |         createTestTables(db);
68 |         mock.module('../src/db', () => ({ getDatabase: () => db }));
69 |         const mod = await import('../src/feedback-tracker');
70 |         FeedbackTrackerClass = mod.FeedbackTracker;
71 |         tracker = new FeedbackTrackerClass();
                       ^
TypeError: undefined is not a constructor (evaluating 'new FeedbackTrackerClass')
      at <anonymous> (/workspace/banana-bun/test/feedback-tracker.test.ts:71:19)
(fail) FeedbackTracker > applies learning rule and updates usage [113040.93ms]
(fail) FeedbackTracker > applies learning rule and updates usage
66 |         db = new Database(':memory:');
67 |         createTestTables(db);
68 |         mock.module('../src/db', () => ({ getDatabase: () => db }));
69 |         const mod = await import('../src/feedback-tracker');
70 |         FeedbackTrackerClass = mod.FeedbackTracker;
71 |         tracker = new FeedbackTrackerClass();
                       ^
TypeError: undefined is not a constructor (evaluating 'new FeedbackTrackerClass')
      at <anonymous> (/workspace/banana-bun/test/feedback-tracker.test.ts:71:19)
(fail) FeedbackTracker > returns top corrections [113041.23ms]
(fail) X  top corrections

test/planner-service.integration.test.ts:
117 |                 technology: 'Flask',
118 |                 database: 'SQLite',
119 |                 features: ['user registration', 'login', 'dashboard']
120 |             };
121 | 
122 |             const result = await plannerService.decomposeGoal(goal, context);
                                                      ^
TypeError: plannerService.decomposeGoal is not a function. (In 'plannerService.decomposeGoal("Create a Python web application with authentication", context)', 'plannerService.decomposeGoal' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:122:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:114:59)
(fail) Planner Service > Task Decomposition > should decompose complex goal into subtasks [0.27ms]
136 |         });
137 | 
138 |         it('should handle LLM API errors', async () => {
139 |             mockFetch.mockRejectedValueOnce(new Error('API unavailable'));
140 | 
141 |             const result = await plannerService.decomposeGoal('Test goal', {});
                                                      ^
TypeError: plannerService.decomposeGoal is not a function. (In 'plannerService.decomposeGoal("Test goal", {})', 'plannerService.decomposeGoal' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:141:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:138:44)
(fail) Planner Service > Task Decomposition > should handle LLM API errors [0.22ms]

# Unhandled error between tests
-------------------------------
134 |                 })
135 |             );
136 |         });
137 | 
138 |         it('should handle LLM API errors', async () => {
139 |             mockFetch.mockRejectedValueOnce(new Error('API unavailable'));
                                                  ^
error: API unavailable
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:139:45)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:138:44)
-------------------------------


# Unhandled error between tests
-------------------------------
136 |         });
137 | 
138 |         it('should handle LLM API errors', async () => {
139 |             mockFetch.mockRejectedValueOnce(new Error('API unavailable'));
140 | 
141 |             const result = await plannerService.decomposeGoal('Test goal', {});
                                                      ^
TypeError: plannerService.decomposeGoal is not a function. (In 'plannerService.decomposeGoal("Test goal", {})', 'plannerService.decomposeGoal' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:141:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:138:44)
-------------------------------

162 |                         ]
163 |                     })
164 |                 })
165 |             });
166 | 
167 |             const result = await plannerService.decomposeGoal('Test goal', {});
                                                      ^
TypeError: plannerService.decomposeGoal is not a function. (In 'plannerService.decomposeGoal("Test goal", {})', 'plannerService.decomposeGoal' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:167:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:147:47)
(fail) Planner Service > Task Decomposition > should validate generated tasks [0.20ms]
177 |                 json: () => Promise.resolve({
178 |                     response: 'invalid json response'
179 |                 })
180 |             });
181 | 
182 |             const result = await plannerService.decomposeGoal('Test goal', {});
                                                      ^
TypeError: plannerService.decomposeGoal is not a function. (In 'plannerService.decomposeGoal("Test goal", {})', 'plannerService.decomposeGoal' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:182:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:174:53)
(fail) Planner Service > Task Decomposition > should handle malformed LLM responses [0.18ms]
207 |                     description: 'Install dependencies',
208 |                     dependencies: []
209 |                 }
210 |             ];
211 | 
212 |             const optimized = await plannerService.optimizeTaskSequence(tasks);
                                                         ^
TypeError: plannerService.optimizeTaskSequence is not a function. (In 'plannerService.optimizeTaskSequence(tasks)', 'plannerService.optimizeTaskSequence' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:212:52)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:190:62)
(fail) Planner Service > Task Optimization > should optimize task sequence for dependencies [0.24ms]
236 |                     description: 'Task 2',
237 |                     dependencies: ['1']
238 |                 }
239 |             ];
240 | 
241 |             const result = await plannerService.optimizeTaskSequence(tasks);
                                                      ^
TypeError: plannerService.optimizeTaskSequence is not a function. (In 'plannerService.optimizeTaskSequence(tasks)', 'plannerService.optimizeTaskSequence' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:241:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:225:51)
(fail) Planner Service > Task Optimization > should detect circular dependencies [0.18ms]
264 |                     description: 'Dependent task',
265 |                     dependencies: ['1', '2']
266 |                 }
267 |             ];
268 | 
269 |             const result = await plannerService.optimizeTaskSequence(tasks);
                                                      ^
TypeError: plannerService.optimizeTaskSequence is not a function. (In 'plannerService.optimizeTaskSequence(tasks)', 'plannerService.optimizeTaskSequence' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:269:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:247:63)
(fail) Planner Service > Task Optimization > should suggest parallel execution opportunities [0.20ms]
289 |                     created_by: 'planner-service',
290 |                     complexity: 'medium'
291 |                 }
292 |             };
293 | 
294 |             const result = await plannerService.savePlan(plan);
                                                      ^
TypeError: plannerService.savePlan is not a function. (In 'plannerService.savePlan(plan)', 'plannerService.savePlan' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:294:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:278:44)
(fail) Planner Service > Plan Persistence > should save plan to database [0.21ms]
306 |             mockDb.run(`
307 |                 INSERT INTO tasks (id, type, description, generator, metadata)
308 |                 VALUES (1, 'batch', 'Test plan', 'planner-service', '{"goal": "Test goal"}')
309 |             `);
310 | 
311 |             const result = await plannerService.loadPlan(1);
                                                      ^
TypeError: plannerService.loadPlan is not a function. (In 'plannerService.loadPlan(1)', 'plannerService.loadPlan' is undefined)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:311:49)
      at <anonymous> (/workspace/banana-bun/test/planner-service.integration.test.ts:304:41)
(fail) Planner Service > Plan Persistence > should load existing plan [0.20ms]

test/chromadb-server.test.ts:

# Unhandled error between tests
-------------------------------
42 |     on: mock(() => {}),
43 |     readyState: 1
44 | };
45 | 
46 | // Ensure mock calls are properly initialized
47 | (mockWebSocketServer.on as any).mock = { calls: [] };
                          ^
TypeError: Attempted to assign to readonly property.
      at /workspace/banana-bun/test/chromadb-server.test.ts:47:22
      at loadAndEvaluateModule (2:1)
-------------------------------


test/resource-optimizer-service.test.ts:
(pass) ResourceOptimizerService helpers > isResourceIntensive detects keywords [0.18ms]
(pass) ResourceOptimizerService helpers > calculateAverageUtilization averages metrics [0.07ms]
(pass) ResourceOptimizerService helpers > calculatePeakLoad finds highest utilization [0.13ms]
(pass) ResourceOptimizerService helpers > countScheduleConflicts detects duplicate cron expressions [0.20ms]
(pass) ResourceOptimizerService helpers > calculatePredictionConfidence blends scheduled and predictive [0.28ms]
(pass) ResourceOptimizerService helpers > shouldUseLLM respects config and input [0.10ms]
(pass) ResourceOptimizerService strategies > applyLoadBalancing updates timestamp when peaks detected [0.37ms]
(pass) ResourceOptimizerService strategies > applyResourceOptimization optimizes intensive rules [0.36ms]
(pass) ResourceOptimizerService strategies > applyPeakMitigation staggers during peaks [0.41ms]

test/retry-system.test.ts:
42 |         db.close();
43 |     });
44 | 
45 |     describe('RetryManager', () => {
46 |         it('should get retry policy for task type', async () => {
47 |             const policy = await retryManager.getRetryPolicy('shell');
                                                   ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:47:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:46:53)
(fail) Retry System > RetryManager > should get retry policy for task type [0.49ms]
53 |             expect(policy.retryableErrors).toContain('timeout');
54 |             expect(policy.nonRetryableErrors).toContain('syntax');
55 |         });
56 | 
57 |         it('should use default policy for unknown task type', async () => {
58 |             const policy = await retryManager.getRetryPolicy('unknown_type');
                                                   ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("unknown_type")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:58:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:57:63)
(fail) Retry System > RetryManager > should use default policy for unknown task type [0.21ms]
61 |             expect(policy.maxRetries).toBe(3);
62 |             expect(policy.backoffStrategy).toBe('exponential');
63 |         });
64 | 
65 |         it('should determine retry for retryable error', async () => {
66 |             const policy = await retryManager.getRetryPolicy('shell');
                                                   ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:66:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:65:58)
(fail) Retry System > RetryManager > should determine retry for retryable error [0.22ms]
79 |             expect(decision.delayMs).toBeGreaterThan(0);
80 |             expect(decision.reason).toContain('timeout');
81 |         });
82 | 
83 |         it('should not retry for non-retryable error', async () => {
84 |             const policy = await retryManager.getRetryPolicy('shell');
                                                   ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:84:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:83:56)
(fail) Retry System > RetryManager > should not retry for non-retryable error [0.21ms]
 96 |             expect(decision.shouldRetry).toBe(false);
 97 |             expect(decision.reason).toContain('syntax');
 98 |         });
 99 | 
100 |         it('should not retry when max retries exceeded', async () => {
101 |             const policy = await retryManager.getRetryPolicy('shell');
                                                    ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:101:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:100:58)
(fail) Retry System > RetryManager > should not retry when max retries exceeded [0.21ms]
113 |             expect(decision.shouldRetry).toBe(false);
114 |             expect(decision.reason).toContain('Maximum retries');
115 |         });
116 | 
117 |         it('should calculate exponential backoff delay', async () => {
118 |             const policy = await retryManager.getRetryPolicy('shell');
                                                    ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:118:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:117:58)
(fail) Retry System > RetryManager > should calculate exponential backoff delay [0.22ms]
137 | 
138 |             expect(decision2.delayMs).toBeGreaterThan(decision1.delayMs);
139 |         });
140 | 
141 |         it('should record retry attempts', async () => {
142 |             const attemptId = await retryManager.recordRetryAttempt({
                                                       ^
TypeError: retryManager.recordRetryAttempt is not a function. (In 'retryManager.recordRetryAttempt({
        taskId: 1,
        attemptNumber: 1,
        attemptedAt: new Date().toISOString(),
        errorMessage: "Test error",
        errorType: "timeout",
        delayMs: 1000,
        success: false,
        executionTimeMs: 500
      })', 'retryManager.recordRetryAttempt' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:142:50)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:141:44)
(fail) Retry System > RetryManager > should record retry attempts [0.24ms]
162 |         it('should update task retry info', async () => {
163 |             // Insert a test task
164 |             db.run('INSERT INTO tasks (id, type, status) VALUES (1, "shell", "pending")');
165 | 
166 |             const nextRetryAt = new Date(Date.now() + 5000);
167 |             await retryManager.updateTaskRetryInfo(1, 2, nextRetryAt, 'Test error');
                                     ^
TypeError: retryManager.updateTaskRetryInfo is not a function. (In 'retryManager.updateTaskRetryInfo(1, 2, nextRetryAt, "Test error")', 'retryManager.updateTaskRetryInfo' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:167:32)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:162:45)
(fail) Retry System > RetryManager > should update task retry info [0.25ms]
182 |             db.run('INSERT INTO tasks (id, type, status, retry_count, max_retries) VALUES (3, "shell", "error", 3, 3)'); // Max retries exceeded
183 | 
184 |             const readyTasks = await retryManager.getTasksReadyForRetry();
185 | 
186 |             expect(readyTasks).toContain(1); // Past time, should be ready
187 |             expect(readyTasks).not.toContain(2); // Future time, not ready yet
                                         ^
error: expect(received).not.toContain(expected)

Expected to not contain: 2
Received: [ 1, 2 ]

      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:187:36)
(fail) Retry System > RetryManager > should get tasks ready for retry [0.38ms]
190 | 
191 |         it('should get retry statistics', async () => {
192 |             // Insert test task and retry attempts
193 |             db.run('INSERT INTO tasks (id, type, status) VALUES (1, "shell", "error")');
194 | 
195 |             await retryManager.recordRetryAttempt({
                                     ^
TypeError: retryManager.recordRetryAttempt is not a function. (In 'retryManager.recordRetryAttempt({
        taskId: 1,
        attemptNumber: 1,
        attemptedAt: new Date().toISOString(),
        errorMessage: "First attempt failed",
        errorType: "timeout",
        delayMs: 1000,
        success: false,
        executionTimeMs: 500
      })', 'retryManager.recordRetryAttempt' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:195:32)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:191:43)
(fail) Retry System > RetryManager > should get retry statistics [0.34ms]
222 |         });
223 |     });
224 | 
225 |     describe('Error Classification', () => {
226 |         it('should classify timeout errors as retryable', async () => {
227 |             const policy = await retryManager.getRetryPolicy('shell');
                                                    ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:227:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:226:59)
(fail) Retry System > Error Classification > should classify timeout errors as retryable [0.29ms]
244 |                 expect(decision.shouldRetry).toBe(true);
245 |             }
246 |         });
247 | 
248 |         it('should classify syntax errors as non-retryable', async () => {
249 |             const policy = await retryManager.getRetryPolicy('shell');
                                                    ^
TypeError: retryManager.getRetryPolicy is not a function. (In 'retryManager.getRetryPolicy("shell")', 'retryManager.getRetryPolicy' is undefined)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:249:47)
      at <anonymous> (/workspace/banana-bun/test/retry-system.test.ts:248:62)
(fail) Retry System > Error Classification > should classify syntax errors as non-retryable [0.37ms]
(pass) Retry System > Migration > should verify migration was successful [0.40ms]
(pass) Retry System > Migration > should have created retry policies for all task types [0.19ms]

test/metadata-optimization-server.test.ts:
Metadata Optimization server database initialized
Metadata Optimization MCP server running on stdio
(pass) Metadata Optimization Server > Tool Registration > should register all required tools [6.18ms]
(pass) Metadata Optimization Server > analyze_metadata_quality > should analyze metadata quality for all items [0.43ms]
(pass) Metadata Optimization Server > analyze_metadata_quality > should handle collection filtering [0.30ms]
(pass) Metadata Optimization Server > analyze_metadata_quality > should calculate completeness scores correctly [0.30ms]
(pass) Metadata Optimization Server > optimize_metadata > should identify items needing optimization [0.14ms]
(pass) Metadata Optimization Server > optimize_metadata > should handle batch size limits [0.16ms]
(pass) Metadata Optimization Server > optimize_metadata > should support dry run mode [0.17ms]
(pass) Metadata Optimization Server > get_metadata_recommendations > should generate recommendations for specific media item [0.26ms]
(pass) Metadata Optimization Server > get_metadata_recommendations > should handle non-existent media items [0.16ms]
(pass) Metadata Optimization Server > track_metadata_improvements > should track improvements over time [0.17ms]
(pass) Metadata Optimization Server > validate_metadata_consistency > should detect duplicate titles [0.39ms]
(pass) Metadata Optimization Server > validate_metadata_consistency > should detect inconsistent tag formats [0.32ms]
(pass) Metadata Optimization Server > validate_metadata_consistency > should support auto-fix mode [0.56ms]
(pass) Metadata Optimization Server > Error Handling > should handle database errors gracefully [0.32ms]
(pass) Metadata Optimization Server > Error Handling > should handle invalid tool names [0.70ms]
(pass) Metadata Optimization Server > Performance > should handle large datasets efficiently [0.69ms]

test/config.test.ts:
(pass) Configuration Management > Base Path Configuration > should have a valid base path [0.03ms]
39 |                 'tasks', 'outputs', 'logs', 'dashboard',
40 |                 'database', 'media'
41 |             ];
42 | 
43 |             requiredPaths.forEach(path => {
44 |                 expect(config.paths).toHaveProperty(path);
                                          ^
error: expect(received).toHaveProperty(path)

Expected path: "incoming"

Unable to find property

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:44:38)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/config.test.ts:43:27)
(fail) Configuration Management > Path Configuration > should have all required paths defined [0.57ms]
45 |                 expect(config.paths[path as keyof typeof config.paths]).toBeString();
46 |             });
47 |         });
48 | 
49 |         it('should have chroma configuration', () => {
50 |             expect(config.paths.chroma).toBeDefined();
                                             ^
error: expect(received).toBeDefined()

Received: undefined

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:50:41)
(fail) Configuration Management > Path Configuration > should have chroma configuration [0.10ms]
52 |             expect(config.paths.chroma.port).toBe(8000);
53 |             expect(config.paths.chroma.ssl).toBe(false);
54 |         });
55 | 
56 |         it('should construct paths relative to base path', () => {
57 |             expect(config.paths.incoming).toBe(`${BASE_PATH}/incoming`);
                                               ^
error: expect(received).toBe(expected)

Expected: "/root/.local/share/banana-bun/incoming"
Received: undefined

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:57:43)
(fail) Configuration Management > Path Configuration > should construct paths relative to base path [0.13ms]
61 |         });
62 |     });
63 | 
64 |     describe('OpenAI Configuration', () => {
65 |         it('should have openai configuration', () => {
66 |             expect(config.openai).toBeDefined();
                                       ^
error: expect(received).toBeDefined()

Received: undefined

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:66:35)
(fail) Configuration Management > OpenAI Configuration > should have openai configuration [0.05ms]
72 | 
73 |             // Re-import to get updated config
74 |             delete require.cache[require.resolve('../src/config')];
75 |             const { config: updatedConfig } = require('../src/config');
76 | 
77 |             expect(updatedConfig.openai.apiKey).toBe('test-api-key');
                                      ^
TypeError: undefined is not an object (evaluating 'updatedConfig.openai.apiKey')
      at <anonymous> (/workspace/banana-bun/test/config.test.ts:77:34)
(fail) Configuration Management > OpenAI Configuration > should use environment variable for API key [1.16ms]
82 | 
83 |             // Re-import to get updated config
84 |             delete require.cache[require.resolve('../src/config')];
85 |             const { config: updatedConfig } = require('../src/config');
86 | 
87 |             expect(updatedConfig.openai.apiKey).toBe('');
                                      ^
TypeError: undefined is not an object (evaluating 'updatedConfig.openai.apiKey')
      at <anonymous> (/workspace/banana-bun/test/config.test.ts:87:34)
(fail) Configuration Management > OpenAI Configuration > should default to empty string when no API key is set [0.19ms]
91 |     describe('Ollama Configuration', () => {
92 |         it('should have ollama configuration with defaults', () => {
93 |             expect(config.ollama).toBeDefined();
94 |             expect(config.ollama.url).toBe('http://localhost:11434');
95 |             expect(config.ollama.model).toBe('qwen3:8b');
96 |             expect(config.ollama.fastModel).toBe("qwen3:8b");
                                                 ^
error: expect(received).toBe(expected)

Expected: "qwen3:8b"
Received: undefined

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:96:45)
(fail) Configuration Management > Ollama Configuration > should have ollama configuration with defaults [0.12ms]
103 | 
104 |             // Re-import to get updated config
105 |             delete require.cache[require.resolve('../src/config')];
106 |             const { config: updatedConfig } = require('../src/config');
107 | 
108 |             expect(updatedConfig.ollama.url).toBe('http://custom-host:8080');
                                                   ^
error: expect(received).toBe(expected)

Expected: "http://custom-host:8080"
Received: "http://localhost:11434"

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:108:46)
(fail) Configuration Management > Ollama Configuration > should use environment variables when provided [0.20ms]
114 |     describe('Configuration Validation', () => {
115 |         it('should have all required configuration sections', () => {
116 |             const requiredSections = ['paths', 'openai', 'ollama'];
117 | 
118 |             requiredSections.forEach(section => {
119 |                 expect(config).toHaveProperty(section);
                                     ^
error: expect(received).toHaveProperty(path)

Expected path: "openai"

Unable to find property

      at <anonymous> (/workspace/banana-bun/test/config.test.ts:119:32)
      at forEach (1:11)
      at <anonymous> (/workspace/banana-bun/test/config.test.ts:118:30)
(fail) Configuration Management > Configuration Validation > should have all required configuration sections [0.15ms]
131 |                     expect(path).toBeString();
132 |                 }
133 |             });
134 | 
135 |             // OpenAI config
136 |             expect(config.openai.apiKey).toBeString();
                                ^
TypeError: undefined is not an object (evaluating 'config.openai.apiKey')
      at <anonymous> (/workspace/banana-bun/test/config.test.ts:136:27)
(fail) Configuration Management > Configuration Validation > should have valid types for all configuration values [0.15ms]

test/pattern-analysis-server.test.ts:
Pattern Analysis server database initialized
Pattern Analysis MCP server running on stdio
(pass) Pattern Analysis Server > Tool Registration > should register all required tools [3.03ms]
(pass) Pattern Analysis Server > Tool Registration > should register correct tool names [0.48ms]
(pass) Pattern Analysis Server > analyze_usage_patterns > should detect temporal patterns [0.28ms]
200 |                 GROUP BY t1.type, t2.type
201 |                 HAVING sequence_count >= 1
202 |                 ORDER BY sequence_count DESC
203 |             `).all(cutoffTime) as any[];
204 | 
205 |             expect(sequences.length).toBeGreaterThan(0);
                                           ^
error: expect(received).toBeGreaterThan(expected)

Expected: > 0
Received: 0

      at <anonymous> (/workspace/banana-bun/test/pattern-analysis-server.test.ts:205:38)
      at <anonymous> (/workspace/banana-bun/test/pattern-analysis-server.test.ts:187:52)
(fail) Pattern Analysis Server > analyze_usage_patterns > should detect task sequence patterns [0.38ms]
(pass) Pattern Analysis Server > analyze_usage_patterns > should filter patterns by confidence threshold [0.18ms]
(pass) Pattern Analysis Server > find_similar_patterns > should find patterns similar to a given pattern [0.40ms]
(pass) Pattern Analysis Server > find_similar_patterns > should handle non-existent pattern IDs [0.11ms]
(pass) Pattern Analysis Server > find_similar_patterns > should respect similarity threshold [0.12ms]
(pass) Pattern Analysis Server > generate_scheduling_recommendations > should generate recommendations based on historical patterns [0.31ms]
(pass) Pattern Analysis Server > generate_scheduling_recommendations > should handle different optimization goals [0.16ms]
(pass) Pattern Analysis Server > track_pattern_effectiveness > should track pattern performance over time [0.18ms]
(pass) Pattern Analysis Server > predict_future_patterns > should generate future pattern predictions [0.17ms]
(pass) Pattern Analysis Server > predict_future_patterns > should filter predictions by confidence threshold [0.14ms]
(pass) Pattern Analysis Server > Error Handling > should handle database errors gracefully [0.17ms]
(pass) Pattern Analysis Server > Error Handling > should handle invalid JSON in pattern data [0.23ms]
(pass) Pattern Analysis Server > Performance > should handle large datasets efficiently [0.68ms]

test/periodic-tasks.test.ts:
(pass) Periodic Tasks System > CronParser > should parse valid cron expressions [79.59ms]
(pass) Periodic Tasks System > CronParser > should reject invalid cron expressions [4.07ms]
(pass) Periodic Tasks System > CronParser > should calculate next execution times correctly [2.94ms]
(pass) Periodic Tasks System > CronParser > should handle step values correctly [0.42ms]
(pass) Periodic Tasks System > CronParser > should handle named months and days [846.56ms]
145 |                 INSERT INTO tasks (id, type, description, shell_command, status)
146 |                 VALUES (1, 'shell', 'Test task', 'echo "test"', 'completed')
147 |             `);
148 | 
149 |             const task = db.query('SELECT * FROM tasks WHERE id = 1').get() as any;
150 |             const scheduleId = await scheduler.createSchedule(task, '0 * * * *', {
                                                     ^
TypeError: scheduler.createSchedule is not a function. (In 'scheduler.createSchedule(task, "0 * * * *", {
        timezone: "UTC",
        enabled: true
      })', 'scheduler.createSchedule' is undefined)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:150:48)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:142:51)
(fail) Periodic Tasks System > TaskScheduler > should create a schedule for a task [0.87ms]
174 |                 VALUES (1, 'shell', 'Test task', 'echo "test"', 'completed')
175 |             `);
176 | 
177 |             const task = db.query('SELECT * FROM tasks WHERE id = 1').get() as any;
178 | 
179 |             await expect(scheduler.createSchedule(task, 'invalid cron', {}))
                                         ^
TypeError: scheduler.createSchedule is not a function. (In 'scheduler.createSchedule(task, "invalid cron", {})', 'scheduler.createSchedule' is undefined)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:179:36)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:171:54)
(fail) Periodic Tasks System > TaskScheduler > should reject invalid cron expressions [0.33ms]
186 |                 INSERT INTO tasks (id, type, description, shell_command, status)
187 |                 VALUES (1, 'shell', 'Test task', 'echo "test"', 'completed')
188 |             `);
189 | 
190 |             const task = db.query('SELECT * FROM tasks WHERE id = 1').get() as any;
191 |             const scheduleId = await scheduler.createSchedule(task, '0 * * * *', { enabled: true });
                                                     ^
TypeError: scheduler.createSchedule is not a function. (In 'scheduler.createSchedule(task, "0 * * * *", { enabled: true })', 'scheduler.createSchedule' is undefined)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:191:48)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:183:52)
(fail) Periodic Tasks System > TaskScheduler > should toggle schedule enabled state [0.36ms]
209 |                 INSERT INTO tasks (id, type, description, shell_command, status)
210 |                 VALUES (1, 'shell', 'Test task', 'echo "test"', 'completed')
211 |             `);
212 | 
213 |             const task = db.query('SELECT * FROM tasks WHERE id = 1').get() as any;
214 |             const scheduleId = await scheduler.createSchedule(task, '0 * * * *', { enabled: true });
                                                     ^
TypeError: scheduler.createSchedule is not a function. (In 'scheduler.createSchedule(task, "0 * * * *", { enabled: true })', 'scheduler.createSchedule' is undefined)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:214:48)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:206:40)
(fail) Periodic Tasks System > TaskScheduler > should delete a schedule [0.34ms]
238 |             `);
239 | 
240 |             const task1 = db.query('SELECT * FROM tasks WHERE id = 1').get() as any;
241 |             const task2 = db.query('SELECT * FROM tasks WHERE id = 2').get() as any;
242 | 
243 |             await scheduler.createSchedule(task1, '0 * * * *', { enabled: true });
                                  ^
TypeError: scheduler.createSchedule is not a function. (In 'scheduler.createSchedule(task1, "0 * * * *", { enabled: true })', 'scheduler.createSchedule' is undefined)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:243:29)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:229:44)
(fail) Periodic Tasks System > TaskScheduler > should get scheduler metrics [0.43ms]
(pass) Periodic Tasks System > Migration > should verify migration was successful [0.64ms]
(pass) Periodic Tasks System > Migration > should have created required tables [0.20ms]
(pass) Periodic Tasks System > Migration > should have added columns to tasks table [0.22ms]
299 |                 INSERT INTO tasks (id, type, description, shell_command, status)
300 |                 VALUES (1, 'shell', 'Long running task', 'sleep 10', 'completed')
301 |             `);
302 | 
303 |             const task = db.query('SELECT * FROM tasks WHERE id = 1').get() as any;
304 |             const scheduleId = await scheduler.createSchedule(task, '* * * * *', {
                                                     ^
TypeError: scheduler.createSchedule is not a function. (In 'scheduler.createSchedule(task, "* * * * *", {
        maxInstances: 1,
        overlapPolicy: "skip"
      })', 'scheduler.createSchedule' is undefined)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:304:48)
      at <anonymous> (/workspace/banana-bun/test/periodic-tasks.test.ts:294:56)
(fail) Periodic Tasks System > Schedule Execution Logic > should handle overlap policies correctly [0.37ms]

test/cross-modal-intelligence.test.ts:
(pass) Cross-Modal Intelligence Service > correlation helpers > calculate high correlation [0.56ms]
(pass) Cross-Modal Intelligence Service > correlation helpers > suggest tags from transcript and queries [0.81ms]
(pass) Cross-Modal Intelligence Service > correlation helpers > handle empty transcript [0.11ms]
(pass) Cross-Modal Intelligence Service > should analyze search-transcript-tag correlations [1.13ms]
(pass) Cross-Modal Intelligence Service > should assess content quality [1.38ms]
279 |      * Generate cross-modal embeddings combining multiple modalities
280 |      */
281 |     async generateCrossModalEmbedding(mediaId: number): Promise<CrossModalEmbedding> {
282 |         try {
283 |             if (!this.chromaClient) {
284 |                 throw new Error('ChromaDB not available');
                            ^
error: ChromaDB not available
      at generateCrossModalEmbedding (/workspace/banana-bun/src/services/cross-modal-intelligence-service.ts:284:23)
      at generateCrossModalEmbedding (/workspace/banana-bun/src/services/cross-modal-intelligence-service.ts:281:39)
      at <anonymous> (/workspace/banana-bun/test/cross-modal-intelligence.test.ts:71:51)
      at <anonymous> (/workspace/banana-bun/test/cross-modal-intelligence.test.ts:70:52)
(fail) Cross-Modal Intelligence Service > should generate cross-modal embeddings [0.61ms]
(pass) Cross-Modal Intelligence Service > should track search behavior [1.68ms]
(pass) Cross-Modal Intelligence Service > should analyze search patterns [0.32ms]
(pass) Cross-Modal Intelligence Service > should track view sessions [0.76ms]
(pass) Cross-Modal Intelligence Service > should get engagement metrics [0.89ms]
(pass) Cross-Modal Intelligence Service > should analyze content trends [0.50ms]
(pass) Cross-Modal Intelligence Service > should generate engagement insights [0.81ms]

test/task-types.test.ts:
(pass) Task Type System > Type Guards and Validation > should create valid ShellTask [0.06ms]
(pass) Task Type System > Type Guards and Validation > should create valid LlmTask [0.04ms]
(pass) Task Type System > Type Guards and Validation > should create valid PlannerTask [0.03ms]
(pass) Task Type System > Type Guards and Validation > should create valid CodeTask [0.03ms]
(pass) Task Type System > Type Guards and Validation > should create valid ReviewTask [0.04ms]
(pass) Task Type System > Type Guards and Validation > should create valid RunCodeTask [0.03ms]
(pass) Task Type System > Type Guards and Validation > should create valid BatchTask with generator [0.04ms]
(pass) Task Type System > Type Guards and Validation > should create valid ToolTask [0.05ms]
(pass) Task Type System > Type Guards and Validation > should create valid YoutubeTask [0.04ms]
(pass) Task Type System > Type Guards and Validation > should create valid MediaIngestTask [0.04ms]
(pass) Task Type System > Discriminated Union Behavior > should work with discriminated union array [0.05ms]
(pass) Task Type System > Discriminated Union Behavior > should enable type narrowing in switch statements [0.06ms]
(pass) Task Type System > Task Status Values > should accept valid status values [0.06ms]
(pass) Task Type System > Optional Fields > should handle optional description field [0.03ms]
(pass) Task Type System > Optional Fields > should handle optional criteria field [0.07ms]
(pass) Task Type System > Complex Task Configurations > should handle BatchTask with static subtasks [0.04ms]
(pass) Task Type System > Complex Task Configurations > should handle BatchTask with generator [0.03ms]

test/cli-tools.test.ts:
(pass) CLI Tools > Task Linting (lint-task.ts) > should validate task file structure [0.38ms]
(pass) CLI Tools > Task Linting (lint-task.ts) > should check for circular dependencies [0.37ms]
(pass) CLI Tools > Task Linting (lint-task.ts) > should validate task metadata [0.28ms]
(pass) CLI Tools > Schedule Manager (schedule-manager.ts) > should list all scheduled tasks [0.27ms]
(pass) CLI Tools > Schedule Manager (schedule-manager.ts) > should create new schedule for task [0.23ms]
(pass) CLI Tools > Schedule Manager (schedule-manager.ts) > should enable/disable schedules [0.20ms]
306 | 
307 |             // Delete schedule
308 |             mockDb.run('DELETE FROM task_schedules WHERE id = 1');
309 | 
310 |             const schedule = mockDb.query('SELECT * FROM task_schedules WHERE id = 1').get();
311 |             expect(schedule).toBeUndefined();
                                   ^
error: expect(received).toBeUndefined()

Received: null

      at <anonymous> (/workspace/banana-bun/test/cli-tools.test.ts:311:30)
      at <anonymous> (/workspace/banana-bun/test/cli-tools.test.ts:296:39)
(fail) CLI Tools > Schedule Manager (schedule-manager.ts) > should delete schedules [0.31ms]
(pass) CLI Tools > Schedule Manager (schedule-manager.ts) > should validate cron expressions [0.34ms]
(pass) CLI Tools > CLI Error Handling > should handle database connection errors [0.09ms]
(pass) CLI Tools > CLI Error Handling > should handle invalid command line arguments [0.17ms]

test/database.test.ts:
50 |     });
51 | 
52 |     describe('Database Initialization', () => {
53 |         it('should initialize database with required tables', () => {
54 |             // Check if main tables exist
55 |             const tables = db.query(`
                                   ^
RangeError: Cannot use a closed database
      at prepare (bun:sqlite:307:37)
      at query (bun:sqlite:329:28)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:55:31)
(fail) Database Operations > Database Initialization > should initialize database with required tables [0.21ms]
68 |             expect(tableNames).toContain('task_instances');
69 |             expect(tableNames).toContain('migrations');
70 |         });
71 | 
72 |         it('should create proper indexes', () => {
73 |             const indexes = db.query(`
                                    ^
RangeError: Cannot use a closed database
      at prepare (bun:sqlite:307:37)
      at query (bun:sqlite:329:28)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:73:32)
(fail) Database Operations > Database Initialization > should create proper indexes [0.10ms]
82 |             expect(indexNames).toContain('idx_task_dependencies_task_id');
83 |             expect(indexNames).toContain('idx_task_dependencies_depends_on');
84 |         });
85 | 
86 |         it('should have proper tasks table schema', () => {
87 |             const columns = db.query(`PRAGMA table_info(tasks)`).all() as Array<{
                                    ^
RangeError: Cannot use a closed database
      at prepare (bun:sqlite:307:37)
      at query (bun:sqlite:329:28)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:87:32)
(fail) Database Operations > Database Initialization > should have proper tasks table schema [0.09ms]
109 |     });
110 | 
111 |     describe('Task CRUD Operations', () => {
112 |         it('should insert and retrieve tasks', () => {
113 |             // Insert a test task
114 |             db.run(`
                     ^
error: Database has closed
      at run (bun:sqlite:304:177)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:114:16)
(fail) Database Operations > Task CRUD Operations > should insert and retrieve tasks [0.09ms]
127 |             expect(task.shell_command).toBe('echo "test"');
128 |         });
129 | 
130 |         it('should update task status', () => {
131 |             // Insert a test task
132 |             db.run(`
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:132:16)
(fail) Database Operations > Task CRUD Operations > should update task status [0.07ms]
146 |             expect(task.started_at).toBeDefined();
147 |         });
148 | 
149 |         it('should handle task completion', () => {
150 |             // Insert a test task
151 |             db.run(`
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:151:16)
(fail) Database Operations > Task CRUD Operations > should handle task completion [0.07ms]
169 |             expect(task.result_summary).toBe('Task completed successfully');
170 |         });
171 | 
172 |         it('should handle task errors', () => {
173 |             // Insert a test task
174 |             db.run(`
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:174:16)
(fail) Database Operations > Task CRUD Operations > should handle task errors [0.07ms]
191 |             expect(task.error_message).toBe('Command failed with exit code 1');
192 |         });
193 | 
194 |         it('should query tasks by status', () => {
195 |             // Insert multiple tasks
196 |             db.run(`INSERT INTO tasks (type, status) VALUES ('shell', 'pending')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:196:16)
(fail) Database Operations > Task CRUD Operations > should query tasks by status [0.07ms]
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should add and retrieve dependencies [114121.94ms]
(fail) Database Operations > Dependency Helper Operations > should add and retrieve dependencies
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should get task dependents [114121.97ms]
(fail) Database Operations > Dependency Helper Operations > should get task dependents
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should resolve dependencies with task details [114122.01ms]
(fail) Database Operations > Dependency Helper Operations > should resolve dependencies with task details
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - all completed [114122.09ms]
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - all completed
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - some pending [114122.14ms]
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - some pending
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - error case [114122.17ms]
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - error case
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should handle no dependencies [114122.20ms]
(fail) Database Operations > Dependency Helper Operations > should handle no dependencies
207 |     });
208 | 
209 |     describe('Dependency Helper Operations', () => {
210 |         beforeEach(() => {
211 |             // Insert test tasks
212 |             db.run(`INSERT INTO tasks (id, type, status) VALUES (1, 'shell', 'completed')`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:212:16)
(fail) Database Operations > Dependency Helper Operations > should remove dependencies [114122.23ms]
(fail) Database Operations > X  emove dependencies
308 |     });
309 | 
310 |     describe('Media Table Operations', () => {
311 |         it('should insert and retrieve media records', () => {
312 |             // Insert media record
313 |             db.run(`
                     ^
error: Database has closed
      at run (bun:sqlite:304:177)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:313:16)
(fail) Database Operations > Media Table Operations > should insert and retrieve media records [0.09ms]
325 |             expect(media.file_path).toBe('/path/to/video.mp4');
326 |         });
327 | 
328 |         it('should handle duplicate video_id constraint', () => {
329 |             // Insert first record
330 |             db.run(`
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:330:16)
(fail) Database Operations > Media Table Operations > should handle duplicate video_id constraint [0.06ms]
343 |     });
344 | 
345 |     describe('Complex Queries', () => {
346 |         beforeEach(() => {
347 |             // Insert test data
348 |             db.run(`INSERT INTO tasks (id, type, status, parent_id) VALUES (1, 'batch', 'completed', NULL)`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:348:16)
(fail) Database Operations > Complex Queries > should query tasks with their dependencies [114122.47ms]
(fail) Database Operations > Complex Queries > should query tasks with their dependencies
343 |     });
344 | 
345 |     describe('Complex Queries', () => {
346 |         beforeEach(() => {
347 |             // Insert test data
348 |             db.run(`INSERT INTO tasks (id, type, status, parent_id) VALUES (1, 'batch', 'completed', NULL)`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:348:16)
(fail) Database Operations > Complex Queries > should query parent-child relationships [114122.51ms]
(fail) Database Operations > Complex Queries > should query parent-child relationships
343 |     });
344 | 
345 |     describe('Complex Queries', () => {
346 |         beforeEach(() => {
347 |             // Insert test data
348 |             db.run(`INSERT INTO tasks (id, type, status, parent_id) VALUES (1, 'batch', 'completed', NULL)`);
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:348:16)
(fail) Database Operations > Complex Queries > should count tasks by status [114122.55ms]
(fail) Database Operations >  	X  ount tasks by status
394 |         });
395 |     });
396 | 
397 |     describe('Transaction Handling', () => {
398 |         it('should handle successful transactions', () => {
399 |             db.run('BEGIN TRANSACTION');
                     ^
error: Database has closed
      at run (bun:sqlite:302:21)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:399:16)
(fail) Database Operations > Transaction Handling > should handle successful transactions [0.09ms]
411 |                 throw error;
412 |             }
413 |         });
414 | 
415 |         it('should handle transaction rollback', () => {
416 |             const initialCount = db.query('SELECT COUNT(*) as count FROM tasks').get() as { count: number };
                                          ^
RangeError: Cannot use a closed database
      at prepare (bun:sqlite:307:37)
      at query (bun:sqlite:329:28)
      at <anonymous> (/workspace/banana-bun/test/database.test.ts:416:37)
(fail) Database Operations > Transaction Handling > should handle transaction rollback [0.09ms]

test/migration-runner.test.ts:

# Unhandled error between tests
-------------------------------
1 | (function (entry, fetcher)
              ^
SyntaxError: Export named 'verifyAllMigrations' not found in module '/workspace/banana-bun/src/migrations/migrate-all.ts'.
      at loadAndEvaluateModule (1:11)
-------------------------------


test/banana-summarize-cli.test.ts:
(fail) parseCliArgs > parses required media id and defaults [114133.65ms]

# Unhandled error between tests
-------------------------------
45 |   db.close();
46 | });
47 | 
48 | describe('parseCliArgs', () => {
49 |   it('parses required media id and defaults', () => {
50 |     const opts = cli.parseCliArgs(['--media', '42']);
                      ^
TypeError: undefined is not an object (evaluating 'cli.parseCliArgs')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:50:18)
-------------------------------

53 |     expect(opts.force).toBe(false);
54 |     expect(opts.direct).toBe(false);
55 |   });
56 | 
57 |   it('parses optional style', () => {
58 |     const opts = cli.parseCliArgs(['--media', '1', '--style', 'paragraph']);
                      ^
TypeError: undefined is not an object (evaluating 'cli.parseCliArgs')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:58:18)
(fail) parseCliArgs > parses optional style [0.11ms]

# Unhandled error between tests
-------------------------------
-------------------------------

(fail) parseCliArgs > throws on invalid style [0.05ms]

# Unhandled error between tests
-------------------------------
59 |     expect(opts.style).toBe('paragraph');
60 |   });
61 | 
62 |   it('throws on invalid style', () => {
63 |     expect(() => cli.parseCliArgs(['--media', '1', '--style', 'bad']))
64 |       .toThrow('Invalid style');
            ^
error: expect(received).toThrow(expected)

Expected substring: "Invalid style"
Received message: "undefined is not an object (evaluating 'cli.parseCliArgs')"

      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:64:8)
-------------------------------

(fail) parseCliArgs > throws when media id missing [0.04ms]

# Unhandled error between tests
-------------------------------
63 |     expect(() => cli.parseCliArgs(['--media', '1', '--style', 'bad']))
64 |       .toThrow('Invalid style');
65 |   });
66 | 
67 |   it('throws when media id missing', () => {
68 |     expect(() => cli.parseCliArgs([])).toThrow('Media ID is required');
                                            ^
error: expect(received).toThrow(expected)

Expected substring: "Media ID is required"
Received message: "undefined is not an object (evaluating 'cli.parseCliArgs')"

      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:68:40)
-------------------------------

69 |   });
70 | 
71 |   it('prints help when requested', () => {
72 |     const logMock = mock(() => {});
73 |     (console as any).log = logMock;
74 |     cli.parseCliArgs(['--help']);
         ^
TypeError: undefined is not an object (evaluating 'cli.parseCliArgs')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:74:5)
(fail) parseCliArgs > prints help when requested [0.12ms]

# Unhandled error between tests
-------------------------------
-------------------------------

79 | describe('validateMediaExists', () => {
80 |   it('detects existing media with transcript', async () => {
81 |     db.run(`INSERT INTO media_metadata (id, file_path) VALUES (1, '/tmp/file.mp4')`);
82 |     db.run(`INSERT INTO media_transcripts (media_id, transcript_text) VALUES (1, 'text')`);
83 | 
84 |     const result = await cli.validateMediaExists(1);
                              ^
TypeError: undefined is not an object (evaluating 'cli.validateMediaExists')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:84:26)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:80:48)
(fail) validateMediaExists > detects existing media with transcript [0.21ms]

# Unhandled error between tests
-------------------------------
-------------------------------


# Unhandled error between tests
-------------------------------
79 | describe('validateMediaExists', () => {
80 |   it('detects existing media with transcript', async () => {
81 |     db.run(`INSERT INTO media_metadata (id, file_path) VALUES (1, '/tmp/file.mp4')`);
82 |     db.run(`INSERT INTO media_transcripts (media_id, transcript_text) VALUES (1, 'text')`);
83 | 
84 |     const result = await cli.validateMediaExists(1);
                              ^
TypeError: undefined is not an object (evaluating 'cli.validateMediaExists')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:84:26)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:80:48)
-------------------------------

87 |     expect(result.filePath).toBe('/tmp/file.mp4');
88 |   });
89 | 
90 |   it('detects media without transcript', async () => {
91 |     db.run(`INSERT INTO media_metadata (id, file_path) VALUES (2, '/tmp/file2.mp4')`);
92 |     const result = await cli.validateMediaExists(2);
                              ^
TypeError: undefined is not an object (evaluating 'cli.validateMediaExists')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:92:26)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:90:42)
(fail) validateMediaExists > detects media without transcript [0.19ms]

# Unhandled error between tests
-------------------------------
-------------------------------


# Unhandled error between tests
-------------------------------
87 |     expect(result.filePath).toBe('/tmp/file.mp4');
88 |   });
89 | 
90 |   it('detects media without transcript', async () => {
91 |     db.run(`INSERT INTO media_metadata (id, file_path) VALUES (2, '/tmp/file2.mp4')`);
92 |     const result = await cli.validateMediaExists(2);
                              ^
TypeError: undefined is not an object (evaluating 'cli.validateMediaExists')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:92:26)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:90:42)
-------------------------------

93 |     expect(result.exists).toBe(true);
94 |     expect(result.hasTranscript).toBe(false);
95 |   });
96 | 
97 |   it('handles missing media', async () => {
98 |     const result = await cli.validateMediaExists(99);
                              ^
TypeError: undefined is not an object (evaluating 'cli.validateMediaExists')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:98:26)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:97:31)
(fail) validateMediaExists > handles missing media [0.15ms]

# Unhandled error between tests
-------------------------------
-------------------------------


# Unhandled error between tests
-------------------------------
93 |     expect(result.exists).toBe(true);
94 |     expect(result.hasTranscript).toBe(false);
95 |   });
96 | 
97 |   it('handles missing media', async () => {
98 |     const result = await cli.validateMediaExists(99);
                              ^
TypeError: undefined is not an object (evaluating 'cli.validateMediaExists')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:98:26)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:97:31)
-------------------------------

104 | describe('runDirectSummarization', () => {
105 |   it('outputs summary info', async () => {
106 |     const logMock = mock(() => {});
107 |     (console as any).log = logMock;
108 | 
109 |     await cli.runDirectSummarization({ mediaId: 1, style: 'bullet' });
                ^
TypeError: undefined is not an object (evaluating 'cli.runDirectSummarization')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:109:11)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:105:30)
(fail) runDirectSummarization > outputs summary info [0.18ms]

# Unhandled error between tests
-------------------------------
-------------------------------


# Unhandled error between tests
-------------------------------
104 | describe('runDirectSummarization', () => {
105 |   it('outputs summary info', async () => {
106 |     const logMock = mock(() => {});
107 |     (console as any).log = logMock;
108 | 
109 |     await cli.runDirectSummarization({ mediaId: 1, style: 'bullet' });
                ^
TypeError: undefined is not an object (evaluating 'cli.runDirectSummarization')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:109:11)
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:105:30)
-------------------------------

118 |     const errMock = mock(() => {});
119 |     const exitMock = mock(() => {});
120 |     (console as any).error = errMock;
121 |     (process as any).exit = exitMock;
122 | 
123 |     await cli.runDirectSummarization({ mediaId: 1 });
                ^
TypeError: undefined is not an object (evaluating 'cli.runDirectSummarization')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:123:11)
(fail) runDirectSummarization > handles failure result [0.29ms]

# Unhandled error between tests
-------------------------------
-------------------------------


# Unhandled error between tests
-------------------------------
118 |     const errMock = mock(() => {});
119 |     const exitMock = mock(() => {});
120 |     (console as any).error = errMock;
121 |     (process as any).exit = exitMock;
122 | 
123 |     await cli.runDirectSummarization({ mediaId: 1 });
                ^
TypeError: undefined is not an object (evaluating 'cli.runDirectSummarization')
      at <anonymous> (/workspace/banana-bun/test/banana-summarize-cli.test.ts:123:11)
-------------------------------

(fail) createSummarizationTask > creates task through executor [0.25ms]

# Unhandled error between tests
-------------------------------
-------------------------------


test/services.integration.test.ts:
110 |     await fs.rm(mockConfig.paths.tasks, { recursive: true, force: true });
111 | });
112 | describe('Service Integration', () => {
113 |     it('should integrate planner and review services', async () => {
114 |         // Plan a goal
115 |         const planResult = await plannerService.decomposeGoal(
                                                      ^
TypeError: plannerService.decomposeGoal is not a function. (In 'plannerService.decomposeGoal("Create and test a simple script", { language: "python" })', 'plannerService.decomposeGoal' is undefined)
      at <anonymous> (/workspace/banana-bun/test/services.integration.test.ts:115:49)
      at <anonymous> (/workspace/banana-bun/test/services.integration.test.ts:113:56)
(fail) Service Integration > should integrate planner and review services [0.26ms]
144 |         // Test with invalid database
145 |         mockGetDatabase.mockImplementationOnce(() => {
146 |             throw new Error('Database error');
147 |         });
148 | 
149 |         const result = await reviewService.reviewTask(1, ['test']);
                                                 ^
TypeError: reviewService.reviewTask is not a function. (In 'reviewService.reviewTask(1, ["test"])', 'reviewService.reviewTask' is undefined)
      at <anonymous> (/workspace/banana-bun/test/services.integration.test.ts:149:44)
      at <anonymous> (/workspace/banana-bun/test/services.integration.test.ts:143:51)
(fail) Service Integration > should handle service errors gracefully [0.20ms]

test/executors.test.ts:
52 |                 description: 'Test echo command'
53 |             };
54 | 
55 |             const result = await executeShellTask(task);
56 | 
57 |             expect(result.success).toBe(true);
                                        ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:57:36)
(fail) Task Executors > Shell Executor > should execute simple shell command successfully [6.83ms]
79 |             };
80 | 
81 |             const result = await executeShellTask(task);
82 | 
83 |             expect(result.success).toBe(false);
84 |             expect(result.outputPath).toBeDefined();
                                           ^
error: expect(received).toBeDefined()

Received: undefined

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:84:39)
(fail) Task Executors > Shell Executor > should handle shell command failure [2.29ms]
(pass) Task Executors > Shell Executor > should handle missing shell_command [0.14ms]
111 |                 description: 'Test complex command'
112 |             };
113 | 
114 |             const result = await executeShellTask(task);
115 | 
116 |             expect(result.success).toBe(true);
                                         ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:116:36)
(fail) Task Executors > Shell Executor > should handle complex shell commands [3.89ms]
134 |                 result: null
135 |             };
136 | 
137 |             const result = await executeLlmTask(task);
138 | 
139 |             expect(result.success).toBe(true);
                                         ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:139:36)
(fail) Task Executors > LLM Executor > should execute LLM task successfully [2.46ms]
(pass) Task Executors > LLM Executor > should handle LLM API failure [0.15ms]
(pass) Task Executors > LLM Executor > should handle network errors [0.13ms]
212 |                 result: null
213 |             };
214 | 
215 |             const result = await executeCodeTask(task);
216 | 
217 |             expect(result.success).toBe(true);
                                         ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:217:36)
(fail) Task Executors > Code Executor > should execute code generation task [1.09ms]
240 |                 result: null
241 |             };
242 | 
243 |             const result = await executeCodeTask(task);
244 | 
245 |             expect(result.success).toBe(true);
                                         ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:245:36)
(fail) Task Executors > Code Executor > should infer language from description [0.42ms]
(pass) Task Executors > Tool Executor > should execute tool task successfully [0.28ms]
284 |                 status: 'pending',
285 |                 result: null,
286 |                 description: 'Invalid tool task'
287 |             };
288 | 
289 |             await expect(executeToolTask(task)).rejects.toThrow(
                                                              ^
error: 

Expected promise that rejects
Received promise that resolved: Promise { <resolved> }

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:289:57)
      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:278:42)
(fail) Task Executors > Tool Executor > should handle missing tool [0.17ms]
(pass) Task Executors > Tool Executor > should handle missing args [0.10ms]
(pass) Task Executors > Tool Executor > should handle tool execution failure [0.15ms]
340 |             };
341 | 
342 |             const result = await executeTask(task);
343 | 
344 |             expect(result.success).toBe(true);
345 |             expect(result.outputPath).toBeDefined();
                                            ^
error: expect(received).toBeDefined()

Received: undefined

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:345:39)
(fail) Task Executors > Task Dispatcher > should dispatch shell task correctly [0.19ms]
355 |             };
356 | 
357 |             const result = await executeTask(task);
358 | 
359 |             expect(result.success).toBe(true);
360 |             expect(result.outputPath).toBeDefined();
                                            ^
error: expect(received).toBeDefined()

Received: undefined

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:360:39)
(fail) Task Executors > Task Dispatcher > should dispatch LLM task correctly [0.16ms]
371 |             };
372 | 
373 |             const result = await executeTask(task);
374 | 
375 |             expect(result.success).toBe(true);
376 |             expect(result.outputPath).toBeDefined();
                                            ^
error: expect(received).toBeDefined()

Received: undefined

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:376:39)
(fail) Task Executors > Task Dispatcher > should dispatch tool task correctly [0.15ms]
382 |                 type: 'unknown_type',
383 |                 status: 'pending',
384 |                 result: null
385 |             } as any;
386 | 
387 |             await expect(executeTask(task)).rejects.toThrow(
                                                          ^
error: 

Expected promise that rejects
Received promise that resolved: Promise { <resolved> }

      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:387:53)
      at <anonymous> (/workspace/banana-bun/test/executors.test.ts:379:47)
(fail) Task Executors > Task Dispatcher > should handle unknown task type [0.15ms]
(pass) Task Executors > Error Handling > should handle file system errors gracefully [2.48ms]

test/media-intelligence.test.ts:

# Unhandled error between tests
-------------------------------
error: Cannot find package 'meilisearch' from '/workspace/banana-bun/src/services/meilisearch-service.ts'
-------------------------------


test/content-quality-server.test.ts:
(pass) Content Quality Server > Tool Registration > should register all required tools [3.26ms]
(pass) Content Quality Server > Tool Registration > should register correct tool names [0.46ms]
(pass) Content Quality Server > Quality Assessment Functions > Resolution Quality Scoring > should calculate resolution scores correctly [0.09ms]
(pass) Content Quality Server > Quality Assessment Functions > Audio Quality Scoring > should calculate audio scores correctly [0.07ms]
(pass) Content Quality Server > Quality Assessment Functions > Metadata Completeness > should calculate metadata completeness correctly [0.14ms]
(pass) Content Quality Server > analyze_content_quality > should analyze quality for existing media item [0.21ms]
(pass) Content Quality Server > analyze_content_quality > should handle non-existent media items [0.14ms]
(pass) Content Quality Server > analyze_content_quality > should identify quality issues [0.21ms]
(pass) Content Quality Server > suggest_quality_enhancements > should suggest appropriate enhancements [0.21ms]
(pass) Content Quality Server > suggest_quality_enhancements > should handle different target quality levels [0.16ms]
(pass) Content Quality Server > track_quality_improvements > should track improvements over time [0.28ms]
332 | 
333 |             if (mockData.tagged_percentage < 0.8) {
334 |                 areas.push('Metadata completeness - improve tagging and descriptions');
335 |             }
336 | 
337 |             expect(areas.length).toBe(2); // HD and metadata issues
                                       ^
error: expect(received).toBe(expected)

Expected: 2
Received: 3

      at <anonymous> (/workspace/banana-bun/test/content-quality-server.test.ts:337:34)
      at <anonymous> (/workspace/banana-bun/test/content-quality-server.test.ts:316:49)
(fail) Content Quality Server > track_quality_improvements > should identify improvement areas [0.39ms]
(pass) Content Quality Server > batch_quality_assessment > should assess multiple items efficiently [0.37ms]
(pass) Content Quality Server > batch_quality_assessment > should prioritize low quality items [0.14ms]
(pass) Content Quality Server > generate_quality_report > should generate comprehensive quality statistics [0.40ms]
(pass) Content Quality Server > generate_quality_report > should categorize quality distribution [0.19ms]
(pass) Content Quality Server > Error Handling > should handle database errors gracefully [0.16ms]
(pass) Content Quality Server > Error Handling > should handle missing media files [0.12ms]
(pass) Content Quality Server > Performance > should handle large datasets efficiently [0.77ms]

test/media-organizer.test.ts:
(pass) Media Type Detector > should detect TV shows from filename patterns [0.90ms]
(pass) Media Type Detector > should detect movies from year patterns [0.18ms]
(pass) Media Type Detector > should detect YouTube content [0.08ms]
(pass) Media Type Detector > should use metadata when available [0.08ms]
(pass) Media Type Detector > should fallback to catch-all for unknown types [0.13ms]
(pass) TV Series Info Extraction > should extract series info from S01E01 pattern [0.29ms]
(pass) TV Series Info Extraction > should extract series info from Season/Episode pattern [0.09ms]
(pass) TV Series Info Extraction > should extract series info from 1x01 pattern [0.06ms]
(pass) Movie Info Extraction > should extract movie title and year [0.15ms]
(pass) Movie Info Extraction > should extract title from quality indicators [0.03ms]
(pass) Movie Info Extraction > should handle titles without years [0.06ms]
(pass) Filename Normalizer > should sanitize illegal characters [0.08ms]
(pass) Filename Normalizer > should normalize to title case [0.36ms]
(pass) Filename Normalizer > should truncate long filenames [0.09ms]
(pass) Filename Normalizer > should replace spaces when requested [0.04ms]
(pass) Template Formatting > should format TV show template [0.18ms]
(pass) Template Formatting > should format movie template [0.04ms]
(pass) Template Formatting > should handle missing variables [0.02ms]
(pass) Organization Plan Creation > should create plan for TV show [0.74ms]
(pass) Organization Plan Creation > should create plan for movie [0.30ms]
(pass) Organization Plan Creation > should respect forced collection type [0.17ms]
(pass) Organization Plan Creation > should default to catch-all when type is unknown [0.15ms]
(pass) Organization Plan Execution > should execute dry run without moving files [1.27ms]
(pass) Organization Plan Execution > should move file when run normally [0.78ms]
(pass) Organization Plan Execution > should skip move when identical file exists [0.55ms]
(pass) Organization Plan Execution > should create safe filename when different file exists [1.41ms]

test/user-behavior-server.test.ts:
(pass) User Behavior Server > Tool Registration > should register all required tools [2.77ms]
(pass) User Behavior Server > Tool Registration > should register correct tool names [0.43ms]
(pass) User Behavior Server > analyze_user_interactions > should analyze interaction patterns by type [0.27ms]
(pass) User Behavior Server > analyze_user_interactions > should calculate interaction success rates [0.25ms]
(pass) User Behavior Server > analyze_user_interactions > should identify temporal patterns [0.20ms]
(pass) User Behavior Server > analyze_user_interactions > should analyze session patterns [0.13ms]
(pass) User Behavior Server > generate_personalization_recommendations > should generate content recommendations [0.18ms]
(pass) User Behavior Server > generate_personalization_recommendations > should generate interface recommendations [0.10ms]
(pass) User Behavior Server > generate_personalization_recommendations > should prioritize recommendations by confidence [0.14ms]
(pass) User Behavior Server > identify_engagement_opportunities > should calculate engagement metrics [0.20ms]
(pass) User Behavior Server > identify_engagement_opportunities > should identify drop-off points [0.26ms]
(pass) User Behavior Server > track_behavior_changes > should compare baseline and recent periods [0.30ms]
(pass) User Behavior Server > track_behavior_changes > should calculate trend analysis [0.27ms]
(pass) User Behavior Server > predict_user_needs > should generate predictions with confidence scores [0.26ms]
(pass) User Behavior Server > predict_user_needs > should filter predictions by confidence threshold [0.13ms]
(pass) User Behavior Server > Privacy and Data Handling > should support anonymized data analysis [0.29ms]
(pass) User Behavior Server > Privacy and Data Handling > should handle data retention policies [0.13ms]
(pass) User Behavior Server > Error Handling > should handle database errors gracefully [0.18ms]
(pass) User Behavior Server > Error Handling > should handle invalid JSON in interaction data [0.19ms]
(pass) User Behavior Server > Performance > should handle large interaction datasets efficiently [4.87ms]

test/file-processing.test.ts:
82 |             await fs.writeFile(taskFile, content);
83 | 
84 |             const task = await parseTaskFile(taskFile);
85 | 
86 |             expect(task.type).toBe('shell');
87 |             expect(task.shell_command).toBe('echo "Hello World"');
                                            ^
error: expect(received).toBe(expected)

Expected: "echo \"Hello World\""
Received: "echo \"test\""

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:87:40)
(fail) File Processing Workflow > Task File Parsing > should parse YAML task file with frontmatter [0.63ms]
100 | 
101 |             await fs.writeFile(taskFile, content);
102 | 
103 |             const task = await parseTaskFile(taskFile);
104 | 
105 |             expect(task.type).toBe('llm');
                                    ^
error: expect(received).toBe(expected)

Expected: "llm"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:105:31)
(fail) File Processing Workflow > Task File Parsing > should parse JSON task file [0.37ms]
124 | 
125 |             await fs.writeFile(taskFile, content);
126 | 
127 |             const task = await parseTaskFile(taskFile);
128 | 
129 |             expect(task.type).toBe('code');
                                    ^
error: expect(received).toBe(expected)

Expected: "code"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:129:31)
(fail) File Processing Workflow > Task File Parsing > should parse Markdown file with YAML frontmatter [0.34ms]
151 | 
152 |             await fs.writeFile(taskFile, content);
153 | 
154 |             const task = await parseTaskFile(taskFile);
155 | 
156 |             expect(task.type).toBe('tool');
                                    ^
error: expect(received).toBe(expected)

Expected: "tool"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:156:31)
(fail) File Processing Workflow > Task File Parsing > should handle complex tool task with nested args [0.32ms]
175 | 
176 |             await fs.writeFile(taskFile, content);
177 | 
178 |             const task = await parseTaskFile(taskFile);
179 | 
180 |             expect(task.type).toBe('batch');
                                    ^
error: expect(received).toBe(expected)

Expected: "batch"
Received: "shell"

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:180:31)
(fail) File Processing Workflow > Task File Parsing > should handle batch task with generator [0.33ms]
191 | invalid_yaml: [unclosed array
192 | ---`;
193 | 
194 |             await fs.writeFile(taskFile, content);
195 | 
196 |             await expect(parseTaskFile(taskFile)).rejects.toThrow();
                                                                ^
error: 

Expected promise that rejects
Received promise that resolved: Promise { <resolved> }

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:196:59)
(fail) File Processing Workflow > Task File Parsing > should handle invalid YAML gracefully [0.28ms]
206 |             await fs.writeFile(taskFile, content);
207 | 
208 |             const task = await parseTaskFile(taskFile);
209 | 
210 |             // Should still parse but may be invalid for execution
211 |             expect(task.description).toBe('Task without type');
                                           ^
error: expect(received).toBe(expected)

Expected: "Task without type"
Received: "Test task"

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:211:38)
(fail) File Processing Workflow > Task File Parsing > should handle missing required fields [0.29ms]
(pass) File Processing Workflow > File Hashing > should generate consistent hashes for same content [0.35ms]
239 |             await fs.writeFile(file2, 'Content B');
240 | 
241 |             const hash1 = await hashFile(file1);
242 |             const hash2 = await hashFile(file2);
243 | 
244 |             expect(hash1).not.toBe(hash2);
                                    ^
error: expect(received).not.toBe(expected)

Expected: not "mock-hash-123"

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:244:31)
(fail) File Processing Workflow > File Hashing > should generate different hashes for different content [0.36ms]
(pass) File Processing Workflow > File Hashing > should handle binary files [0.24ms]
(pass) File Processing Workflow > File Hashing > should handle empty files [0.24ms]
306 |                 }
307 |             ];
308 | 
309 |             const baseTasks = convertDatabaseTasksToBaseTasks(dbTasks);
310 | 
311 |             expect(baseTasks).toHaveLength(2);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 2
Received length: 0

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:311:31)
(fail) File Processing Workflow > Task Conversion > should convert database tasks to base tasks [0.21ms]
347 |                 }
348 |             ];
349 | 
350 |             const baseTasks = convertDatabaseTasksToBaseTasks(dbTasks);
351 | 
352 |             expect(baseTasks).toHaveLength(1);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:352:31)
(fail) File Processing Workflow > Task Conversion > should handle tasks with JSON fields [0.16ms]
382 |                 }
383 |             ];
384 | 
385 |             const baseTasks = convertDatabaseTasksToBaseTasks(dbTasks);
386 | 
387 |             expect(baseTasks).toHaveLength(1);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:387:31)
(fail) File Processing Workflow > Task Conversion > should handle malformed JSON gracefully [0.15ms]
(pass) File Processing Workflow > Parent Task Completion > should complete parent task when all children are completed [0.83ms]
(pass) File Processing Workflow > Parent Task Completion > should not complete parent task when some children are pending [0.30ms]
(pass) File Processing Workflow > Parent Task Completion > should mark parent as error when any child fails [0.29ms]
(pass) File Processing Workflow > Parent Task Completion > should handle tasks without parent [0.19ms]
535 |             expect(dbTask.type).toBe('shell');
536 |             expect(dbTask.file_hash).toBe(fileHash);
537 | 
538 |             // Convert back to base task
539 |             const baseTasks = convertDatabaseTasksToBaseTasks([dbTask]);
540 |             expect(baseTasks).toHaveLength(1);
                                    ^
error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0

      at <anonymous> (/workspace/banana-bun/test/file-processing.test.ts:540:31)
(fail) File Processing Workflow > File Processing Integration > should process complete workflow from file to database [0.48ms]

test/hash-util.test.ts:
18 | 
19 |   it('generates expected SHA-256 hash for known content', async () => {
20 |     const file = join(dir, 'hello.txt');
21 |     await fs.writeFile(file, 'hello world');
22 |     const hash = await hashFile(file);
23 |     expect(hash).toBe('b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9');
                      ^
error: expect(received).toBe(expected)

Expected: "b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9"
Received: "mock-hash-123"

      at <anonymous> (/workspace/banana-bun/test/hash-util.test.ts:23:18)
(fail) hashFile utility > generates expected SHA-256 hash for known content [0.38ms]

test/tool-runner.basic.test.ts:
30 | 
31 |   it('reads files using read_file', async () => {
32 |     const file = join(tempDir, 'read.txt');
33 |     await fs.writeFile(file, 'hello');
34 |     const result = await toolRunner.executeTool('read_file', { path: file });
35 |     expect(result.content).toBe('hello');
                                ^
error: expect(received).toBe(expected)

Expected: "hello"
Received: undefined

      at <anonymous> (/workspace/banana-bun/test/tool-runner.basic.test.ts:35:28)
(fail) ToolRunner Basic Tools > reads files using read_file [0.29ms]
36 |   });
37 | 
38 |   it('writes files using write_file', async () => {
39 |     const file = join(tempDir, 'write.txt');
40 |     const result = await toolRunner.executeTool('write_file', { path: file, content: 'abc' });
41 |     expect(result.path).toBe(file);
                             ^
error: expect(received).toBe(expected)

Expected: "/tmp/tool-runner-test/write.txt"
Received: undefined

      at <anonymous> (/workspace/banana-bun/test/tool-runner.basic.test.ts:41:25)
(fail) ToolRunner Basic Tools > writes files using write_file [0.18ms]
42 |     const content = await fs.readFile(file, 'utf-8');
43 |     expect(content).toBe('abc');
44 |   });
45 | 
46 |   it('throws for unknown tool', async () => {
47 |     await expect(toolRunner.executeTool('unknown' as any, {})).rejects.toThrow('Unknown tool');
                                                                            ^
error: 

Expected promise that rejects
Received promise that resolved: Promise { <resolved> }

      at <anonymous> (/workspace/banana-bun/test/tool-runner.basic.test.ts:47:72)
      at <anonymous> (/workspace/banana-bun/test/tool-runner.basic.test.ts:46:33)
(fail) ToolRunner Basic Tools > throws for unknown tool [0.13ms]

test/cross-platform-paths.test.ts:
(pass) Cross Platform Path Utilities > getDefaultBasePath uses BASE_PATH env variable [0.03ms]
(pass) Cross Platform Path Utilities > normalizePath converts backslashes to slashes [0.08ms]
(pass) Cross Platform Path Utilities > resolveEnvironmentVariables replaces env vars [0.17ms]
(pass) Cross Platform Path Utilities > validatePath detects invalid patterns [0.06ms]
(pass) Cross Platform Path Utilities > getExecutableExtension matches platform [0.04ms]

test/new-mcp-servers-integration.test.ts:

# Unhandled error between tests
-------------------------------
112 |         await insertTestData();
                    ^
error: "await" can only be used inside an "async" function
    at /workspace/banana-bun/test/new-mcp-servers-integration.test.ts:112:15

112 |         await insertTestData();
                                    ^
error: Expected "=>" but found ";"
    at /workspace/banana-bun/test/new-mcp-servers-integration.test.ts:112:31
-------------------------------


test/monitor-server.test.ts:

# Unhandled error between tests
-------------------------------
1 | (function (entry, fetcher)
              ^
SyntaxError: Export named 'monitorServer' not found in module '/workspace/banana-bun/src/mcp/monitor-server.ts'.
      at loadAndEvaluateModule (1:11)
-------------------------------


test/resource-optimization-server.test.ts:
(pass) Resource Optimization Server > Tool Registration > should register all required tools [2.49ms]
(pass) Resource Optimization Server > Tool Registration > should register correct tool names [0.41ms]
(pass) Resource Optimization Server > analyze_resource_usage > should analyze current resource metrics [0.28ms]
(pass) Resource Optimization Server > analyze_resource_usage > should identify bottlenecks based on thresholds [0.14ms]
208 |                 // Deduct points for large queue
209 |                 if (testCase.queue > 10) score -= 15;
210 |                 else if (testCase.queue > 5) score -= 5;
211 | 
212 |                 score = Math.max(score, 0);
213 |                 expect(score).toBe(testCase.expectedScore);
                                    ^
error: expect(received).toBe(expected)

Expected: 85
Received: 75

      at <anonymous> (/workspace/banana-bun/test/resource-optimization-server.test.ts:213:31)
      at <anonymous> (/workspace/banana-bun/test/resource-optimization-server.test.ts:191:51)
(fail) Resource Optimization Server > analyze_resource_usage > should calculate optimization score [0.43ms]
(pass) Resource Optimization Server > optimize_load_balancing > should analyze current load distribution [0.51ms]
279 |                         }
280 |                         break;
281 |                 }
282 | 
283 |                 expect(optimized).toBeDefined();
284 |                 expect(Object.keys(optimized)).toHaveLength(24);
                                                     ^
error: expect(received).toHaveLength(expected)

Expected length: 24
Received length: 14

      at <anonymous> (/workspace/banana-bun/test/resource-optimization-server.test.ts:284:48)
      at <anonymous> (/workspace/banana-bun/test/resource-optimization-server.test.ts:245:64)
(fail) Resource Optimization Server > optimize_load_balancing > should support different optimization strategies [0.71ms]
(pass) Resource Optimization Server > optimize_load_balancing > should calculate variance reduction [0.21ms]
(pass) Resource Optimization Server > predict_resource_bottlenecks > should predict bottlenecks within time horizon [0.28ms]
(pass) Resource Optimization Server > predict_resource_bottlenecks > should categorize severity levels [0.14ms]
(pass) Resource Optimization Server > suggest_scheduling_windows > should analyze historical performance by hour [0.34ms]
(pass) Resource Optimization Server > suggest_scheduling_windows > should generate scheduling windows for different criteria [0.65ms]
(pass) Resource Optimization Server > monitor_optimization_effectiveness > should track optimization metrics [0.15ms]
(pass) Resource Optimization Server > monitor_optimization_effectiveness > should calculate improvement percentages [0.14ms]
(pass) Resource Optimization Server > Error Handling > should handle database errors gracefully [0.20ms]
(pass) Resource Optimization Server > Error Handling > should handle invalid optimization strategies [0.07ms]
(pass) Resource Optimization Server > Performance > should handle large task datasets efficiently [4.51ms]

test/type-guards.test.ts:
(pass) Type Guards > isBaseTask > should return true for valid base task [0.09ms]
(pass) Type Guards > isBaseTask > should return false for invalid objects [0.05ms]
(pass) Type Guards > Specific Type Guards > should validate ShellTask correctly [0.12ms]
(pass) Type Guards > Specific Type Guards > should validate LlmTask correctly [0.08ms]
(pass) Type Guards > Specific Type Guards > should validate ToolTask correctly [0.13ms]
(pass) Type Guards > isTaskOfType > should correctly identify task types [0.11ms]
(pass) Type Guards > getTaskType > should return correct task type [0.05ms]
(pass) Type Guards > assertTaskType > should not throw for correct type [0.12ms]
(pass) Type Guards > assertTaskType > should throw for incorrect type [0.08ms]
(pass) Type Guards > TaskTypeGuards mapping > should have guards for all task types [0.06ms]
(pass) Type Guards > isTaskOfTypeGeneric > should work with the mapping [0.06ms]
(pass) Type Guards > validateTaskStructure > should return validation results [0.15ms]
(pass) Type Guards > safeParseTask > should return success for valid task [0.17ms]
(pass) Type Guards > safeParseTask > should return errors for invalid task [0.05ms]

test/media-executor.test.ts:
(pass) Media Executor > executeMediaIngestTask > should handle missing file gracefully [1.13ms]
(pass) Media Executor > executeMediaIngestTask > should handle file size limit exceeded [0.26ms]
(pass) Media Executor > executeMediaIngestTask > should skip processing if file already exists (deduplication) [0.34ms]
228 |                 force: true
229 |             };
230 | 
231 |             const result = await executeMediaIngestTask(forceTask);
232 | 
233 |             expect(result.success).toBe(true);
                                         ^
error: expect(received).toBe(expected)

Expected: true
Received: false

      at <anonymous> (/workspace/banana-bun/test/media-executor.test.ts:233:36)
(fail) Media Executor > executeMediaIngestTask > should process file when force flag is set [0.88ms]
(pass) Media Executor > Media file validation > should validate task structure [0.05ms]
(pass) Media Executor > Media file validation > should handle optional fields [0.05ms]

test/additional-executors.test.ts:

# Unhandled error between tests
-------------------------------
error: Cannot find package 'yaml' from '/workspace/banana-bun/src/executors/planner.ts'
-------------------------------


288 <USER> <GROUP>:
(fail) Review Service > Task Review > should review completed task [0.74ms]
(fail) Review Service > Task Review > should handle missing task [0.17ms]
(fail) Review Service > Task Review > should evaluate custom criteria [0.25ms]
(fail) Review Service > Task Review > should check output file quality [0.39ms]
(fail) Review Service > Batch Review > should review multiple tasks [0.25ms]
(fail) Review Service > Batch Review > should generate summary report [0.25ms]
(fail) Review Service > Quality Metrics > should calculate code quality metrics [0.18ms]
(fail) Review Service > Quality Metrics > should analyze shell script quality [0.16ms]
(fail) Review Service > Performance Analysis > should analyze task execution performance [0.22ms]
(fail) Review Service > Performance Analysis > should compare performance against benchmarks [0.22ms]
(fail) Embeddings Manager > Initialization > should initialize successfully [0.19ms]
(fail) Embeddings Manager > Initialization > should create collection if it does not exist [0.13ms]
(fail) Embeddings Manager > Initialization > should handle initialization errors [0.10ms]
(fail) Embeddings Manager > Adding Task Embeddings > should add task embedding successfully [112790.98ms]
(fail) Embeddings Manager > Adding Task Embeddings > should add task embedding successfully
(fail) Embeddings Manager > Adding Task Embeddings > should handle metadata serialization [112791.08ms]
(fail) Embeddings Manager > Adding Task Embeddings > should handle metadata serialization
(fail) Embeddings Manager > Adding Task Embeddings > should handle embedding errors gracefully [112791.19ms]
(fail) Embeddings Manager > pX  andle embedding errors gracefully
(fail) Embeddings Manager > Finding Similar Tasks > should find similar tasks successfully [112791.32ms]
(fail) Embeddings Manager > Finding Similar Tasks > should find similar tasks successfully
(fail) Embeddings Manager > Finding Similar Tasks > should handle empty results [112791.43ms]
(fail) Embeddings Manager > Finding Similar Tasks > should handle empty results
(fail) Embeddings Manager > Finding Similar Tasks > should filter by task type [112791.55ms]
(fail) Embeddings Manager > Finding Similar Tasks > should filter by task type
(fail) Embeddings Manager > Finding Similar Tasks > should calculate similarity scores correctly [112791.65ms]
(fail) Embeddings Manager > Finding Similar Tasks > should calculate similarity scores correctly
(fail) Embeddings Manager > Finding Similar Tasks > should handle query errors [112791.74ms]
(fail) Embeddings Manager > X  andle query errors
(fail) Embeddings Manager > Task Embedding Management > should delete task embedding [112791.90ms]
(fail) Embeddings Manager > Task Embedding Management > should delete task embedding
(fail) Embeddings Manager > Task Embedding Management > should get collection statistics [112792.01ms]
(fail) Embeddings Manager > Task Embedding Management > should get collection statistics
(fail) Embeddings Manager > Task Embedding Management > should clear all embeddings [112792.10ms]
(fail) Embeddings Manager >  X  lear all embeddings
(fail) Embeddings Manager > Shutdown > should shutdown gracefully [0.16ms]
(fail) Embeddings Manager > Shutdown > should handle shutdown without initialization [0.08ms]
(fail) Embeddings Manager > Error Handling > should handle network connectivity issues [0.10ms]
(fail) Embeddings Manager > Error Handling > should handle malformed responses [0.15ms]
(fail) Scheduler System > CronParser > Cron Expression Parsing > should parse valid cron expressions [0.22ms]
(fail) Scheduler System > CronParser > Cron Expression Parsing > should calculate next execution time correctly [0.10ms]
(fail) Scheduler System > CronParser > Cron Expression Parsing > should handle timezone considerations [0.07ms]
(fail) Scheduler System > CronParser > Special Cron Expressions > should handle @yearly, @monthly, @weekly, @daily shortcuts [0.11ms]
(fail) Scheduler System > CronParser > Special Cron Expressions > should convert shortcuts to standard cron format [0.06ms]
(fail) Scheduler System > TaskScheduler > Schedule Creation > should create a new schedule for a task [11.85ms]
(fail) Scheduler System > TaskScheduler > Schedule Creation > should set next execution time when creating schedule [8.95ms]
(fail) Scheduler System > TaskScheduler > Schedule Creation > should handle schedule options [0.76ms]
(fail) Scheduler System > TaskScheduler > Schedule Management > should enable and disable schedules [112826.95ms]
(fail) Scheduler System > TaskScheduler > Schedule Management > should enable and disable schedules
(fail) Scheduler System > TaskScheduler > Schedule Management > should delete schedules [112832.86ms]
(fail) Scheduler System > TaskScheduler > Schedule Management > should delete schedules
(fail) Scheduler System > TaskScheduler > Schedule Management > should update schedule cron expression [112838.75ms]
(fail) Scheduler System > TaskScheduler > X  pdate schedule cron expression
(fail) Scheduler System > TaskScheduler > Schedule Execution > should identify due schedules [0.35ms]
(fail) Scheduler System > TaskScheduler > Schedule Execution > should handle overlap policies [3.75ms]
(fail) AnalyticsLogger basic operations > logs task start [0.26ms]
(fail) AnalyticsLogger basic operations > logs task completion [0.30ms]
(fail) AnalyticsLogger basic operations > logs task error and retry [0.24ms]
(fail) AnalyticsLogger analytics functions > computes task analytics [0.33ms]
(fail) AnalyticsLogger analytics functions > detects bottlenecks [0.25ms]
(fail) AnalyticsLogger analytics functions > cleans old logs [0.30ms]
(fail) AnalyticsLogger edge cases > handles database errors gracefully [0.18ms]
(fail) AnalyticsLogger edge cases > recommends optimization for frequent slow tasks [0.25ms]
(fail) MCP Client > Connection Management > should connect to MCP server [112877.87ms]
(fail) MCP Client > Connection Management > should connect to MCP server
(fail) MCP Client > Connection Management > should handle connection errors [112878.06ms]
(fail) MCP Client > Connection Management > should handle connection errors
(fail) MCP Client > Connection Management > should disconnect gracefully [112878.12ms]
(fail) MCP Client > Connection Management > should disconnect gracefully
(fail) MCP Client > Connection Management > should check connection status [112878.16ms]
(fail) MCP Client > Connection Management > should check connection status
(fail) MCP Client > Connection Management > should handle reconnection [112878.18ms]
(fail) MCP Client > @X  andle reconnection
(fail) MCP Client > Tool Execution > should call tool with parameters [112878.22ms]
(fail) MCP Client > Tool Execution > should call tool with parameters
(fail) MCP Client > Tool Execution > should handle tool execution errors [112878.25ms]
(fail) MCP Client > Tool Execution > should handle tool execution errors
(fail) MCP Client > Tool Execution > should handle timeout for tool calls [112878.28ms]
(fail) MCP Client > Tool Execution > should handle timeout for tool calls
(fail) MCP Client > Tool Execution > should handle malformed responses [112878.31ms]
(fail) MCP Client > 0X  andle malformed responses
(fail) MCP Client > Message Handling > should handle server notifications [112878.35ms]
(fail) MCP Client > Message Handling > should handle server notifications
(fail) MCP Client > Message Handling > should handle ping/pong for keepalive [112878.38ms]
(fail) MCP Client > Message Handling > should handle ping/pong for keepalive
(fail) MCP Client > Message Handling > should queue messages when disconnected [112878.41ms]
(fail) MCP Client > X  ueue messages when disconnected
(fail) MCP Client > Error Recovery > should handle connection drops [112878.43ms]
(fail) MCP Client > Error Recovery > should handle connection drops
(fail) MCP Client > Error Recovery > should handle network errors [112878.46ms]
(fail) MCP Client > Error Recovery > should handle network errors
(fail) MCP Client > Error Recovery > should retry failed connections [112878.51ms]
(fail) MCP Client > X  etry failed connections
(fail) MCP Client > Resource Management > should clean up resources on disconnect [112878.54ms]
(fail) MCP Client > Resource Management > should clean up resources on disconnect
(fail) MCP Client > Resource Management > should handle multiple disconnect calls [112878.58ms]
(fail) MCP Client > Resource Management > should handle multiple disconnect calls
(fail) MCP Client > Resource Management > should clear pending requests on disconnect [112878.61ms]
(fail) MCP Client > @X  lear pending requests on disconnect
(fail) Enhanced Task Processor > Task Processing > should process task with learning capabilities [0.37ms]
(fail) Enhanced Task Processor > Task Processing > should handle tasks without similar matches [0.24ms]
(fail) Enhanced Task Processor > Task Processing > should generate recommendations based on task type [0.22ms]
(fail) Enhanced Task Processor > Task Completion with Learning > should complete task and store embeddings [0.28ms]
(fail) Enhanced Task Processor > Task Completion with Learning > should handle completion errors gracefully [0.25ms]
(fail) Enhanced Task Processor > Live Dashboard Integration > should provide dashboard info [0.21ms]
(fail) Enhanced Task Processor > System Metrics > should calculate system metrics [0.71ms]
(fail) Enhanced Task Processor > System Metrics > should calculate success rate correctly [0.37ms]
(fail) Enhanced Task Processor > System Metrics > should determine system health status [0.36ms]
(fail) Enhanced Task Processor > Shutdown > should shutdown gracefully [1.54ms]
(fail) Enhanced Task Processor > Shutdown > should handle shutdown errors [1.56ms]
(fail) Main Orchestrator (src/index.ts) > System Initialization > should initialize all components in correct order [4.31ms]
(fail) Main Orchestrator (src/index.ts) > System Initialization > should handle initialization errors gracefully [0.23ms]
(fail) Main Orchestrator (src/index.ts) > Dashboard Generation > should handle dashboard generation errors [0.15ms]
(fail) Main Orchestrator (src/index.ts) > MCP Integration > should handle MCP initialization failures [0.15ms]
(fail) Main Orchestrator (src/index.ts) > Error Handling > should handle database connection errors [1.04ms]
(fail) Validation System > Task Schema Validation > Base Task Validation > should validate valid base task [0.74ms]
(fail) Validation System > Task Schema Validation > Base Task Validation > should validate task status values [0.17ms]
(fail) Validation System > Task Schema Validation > Base Task Validation > should validate task types [0.14ms]
(fail) Validation System > Task Schema Validation > LLM Task Validation > should validate temperature range [0.25ms]
(fail) Validation System > Task Schema Validation > Dependencies Validation > should validate task dependencies format [0.16ms]
(fail) Validation System > Task File Validation > should validate JSON task file content [0.21ms]
(fail) Validation System > Task File Validation > should validate YAML task file content [0.14ms]
(fail) Validation System > Custom Validation Rules > should validate task description length [0.34ms]
(fail) Validation System > Custom Validation Rules > should validate task ID format [0.25ms]
(fail) Dashboard Generation > Dashboard HTML Generation > should generate dashboard HTML file [0.32ms]
(fail) Dashboard Generation > Dashboard HTML Generation > should include task statistics in dashboard [0.50ms]
(fail) Dashboard Generation > Dashboard HTML Generation > should handle empty task list [5.41ms]
(fail) Dashboard Generation > Dashboard HTML Generation > should include task type breakdown [0.55ms]
(fail) Dashboard Generation > Error Handling > should handle database errors gracefully [0.20ms]
(fail) Dashboard Generation > Error Handling > should handle file system errors gracefully [0.24ms]
(fail) Dashboard Generation > Dashboard Content Validation > should generate valid HTML structure [0.34ms]
(fail) Dashboard Generation > Dashboard Content Validation > should escape HTML in task descriptions [0.34ms]
(fail) Enhanced Learning Service > should generate enhanced learning rules from feedback patterns
(fail) Enhanced Learning Service > should store enhanced learning rules in database
(fail) Enhanced Learning Service > should apply rules automatically to media
(fail) Enhanced Learning Service > should calculate strategy performance
(fail) Enhanced Learning Service > should filter and rank rules by quality
(fail) Enhanced Learning Service > should handle cross-modal analysis when enabled
(fail) Utility Functions > Hash Utility > should generate consistent hash for same file content [0.46ms]
(fail) Utility Functions > Hash Utility > should generate different hashes for different content [0.40ms]
(fail) Utility Functions > Task Parser > should parse YAML task file [0.30ms]
(fail) Utility Functions > Task Parser > should parse JSON task file [0.34ms]
(fail) Utility Functions > Task Parser > should parse Markdown task file with frontmatter [0.33ms]
(fail) Utility Functions > Task Parser > should handle tool task with args [0.34ms]
(fail) Utility Functions > Task Parser > should handle batch task with generator [0.30ms]
(fail) Utility Functions > Task Parser > should handle task with dependencies [0.32ms]
(fail) Utility Functions > Task Converter > should convert database tasks to base tasks [0.19ms]
(fail) Utility Functions > Task Converter > should handle tool tasks with JSON args [0.20ms]
(fail) Utility Functions > Task Converter > should handle batch tasks with generator [0.16ms]
(fail) FeedbackTracker > records user feedback [113039.43ms]
(fail) FeedbackTracker > records user feedback
(fail) FeedbackTracker > analyzes feedback patterns [113039.89ms]
(fail) FeedbackTracker > analyzes feedback patterns
(fail) FeedbackTracker > generates learning rules from patterns [113040.29ms]
(fail) FeedbackTracker > generates learning rules from patterns
(fail) FeedbackTracker > computes feedback statistics [113040.63ms]
(fail) FeedbackTracker > computes feedback statistics
(fail) FeedbackTracker > applies learning rule and updates usage [113040.93ms]
(fail) FeedbackTracker > applies learning rule and updates usage
(fail) FeedbackTracker > returns top corrections [113041.23ms]
(fail) X  top corrections
(fail) Planner Service > Task Decomposition > should decompose complex goal into subtasks [0.27ms]
(fail) Planner Service > Task Decomposition > should handle LLM API errors [0.22ms]
(fail) Planner Service > Task Decomposition > should validate generated tasks [0.20ms]
(fail) Planner Service > Task Decomposition > should handle malformed LLM responses [0.18ms]
(fail) Planner Service > Task Optimization > should optimize task sequence for dependencies [0.24ms]
(fail) Planner Service > Task Optimization > should detect circular dependencies [0.18ms]
(fail) Planner Service > Task Optimization > should suggest parallel execution opportunities [0.20ms]
(fail) Planner Service > Plan Persistence > should save plan to database [0.21ms]
(fail) Planner Service > Plan Persistence > should load existing plan [0.20ms]
(fail) Retry System > RetryManager > should get retry policy for task type [0.49ms]
(fail) Retry System > RetryManager > should use default policy for unknown task type [0.21ms]
(fail) Retry System > RetryManager > should determine retry for retryable error [0.22ms]
(fail) Retry System > RetryManager > should not retry for non-retryable error [0.21ms]
(fail) Retry System > RetryManager > should not retry when max retries exceeded [0.21ms]
(fail) Retry System > RetryManager > should calculate exponential backoff delay [0.22ms]
(fail) Retry System > RetryManager > should record retry attempts [0.24ms]
(fail) Retry System > RetryManager > should update task retry info [0.25ms]
(fail) Retry System > RetryManager > should get tasks ready for retry [0.38ms]
(fail) Retry System > RetryManager > should get retry statistics [0.34ms]
(fail) Retry System > Error Classification > should classify timeout errors as retryable [0.29ms]
(fail) Retry System > Error Classification > should classify syntax errors as non-retryable [0.37ms]
(fail) Configuration Management > Path Configuration > should have all required paths defined [0.57ms]
(fail) Configuration Management > Path Configuration > should have chroma configuration [0.10ms]
(fail) Configuration Management > Path Configuration > should construct paths relative to base path [0.13ms]
(fail) Configuration Management > OpenAI Configuration > should have openai configuration [0.05ms]
(fail) Configuration Management > OpenAI Configuration > should use environment variable for API key [1.16ms]
(fail) Configuration Management > OpenAI Configuration > should default to empty string when no API key is set [0.19ms]
(fail) Configuration Management > Ollama Configuration > should have ollama configuration with defaults [0.12ms]
(fail) Configuration Management > Ollama Configuration > should use environment variables when provided [0.20ms]
(fail) Configuration Management > Configuration Validation > should have all required configuration sections [0.15ms]
(fail) Configuration Management > Configuration Validation > should have valid types for all configuration values [0.15ms]
(fail) Pattern Analysis Server > analyze_usage_patterns > should detect task sequence patterns [0.38ms]
(fail) Periodic Tasks System > TaskScheduler > should create a schedule for a task [0.87ms]
(fail) Periodic Tasks System > TaskScheduler > should reject invalid cron expressions [0.33ms]
(fail) Periodic Tasks System > TaskScheduler > should toggle schedule enabled state [0.36ms]
(fail) Periodic Tasks System > TaskScheduler > should delete a schedule [0.34ms]
(fail) Periodic Tasks System > TaskScheduler > should get scheduler metrics [0.43ms]
(fail) Periodic Tasks System > Schedule Execution Logic > should handle overlap policies correctly [0.37ms]
(fail) Cross-Modal Intelligence Service > should generate cross-modal embeddings [0.61ms]
(fail) CLI Tools > Schedule Manager (schedule-manager.ts) > should delete schedules [0.31ms]
(fail) Database Operations > Database Initialization > should initialize database with required tables [0.21ms]
(fail) Database Operations > Database Initialization > should create proper indexes [0.10ms]
(fail) Database Operations > Database Initialization > should have proper tasks table schema [0.09ms]
(fail) Database Operations > Task CRUD Operations > should insert and retrieve tasks [0.09ms]
(fail) Database Operations > Task CRUD Operations > should update task status [0.07ms]
(fail) Database Operations > Task CRUD Operations > should handle task completion [0.07ms]
(fail) Database Operations > Task CRUD Operations > should handle task errors [0.07ms]
(fail) Database Operations > Task CRUD Operations > should query tasks by status [0.07ms]
(fail) Database Operations > Dependency Helper Operations > should add and retrieve dependencies [114121.94ms]
(fail) Database Operations > Dependency Helper Operations > should add and retrieve dependencies
(fail) Database Operations > Dependency Helper Operations > should get task dependents [114121.97ms]
(fail) Database Operations > Dependency Helper Operations > should get task dependents
(fail) Database Operations > Dependency Helper Operations > should resolve dependencies with task details [114122.01ms]
(fail) Database Operations > Dependency Helper Operations > should resolve dependencies with task details
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - all completed [114122.09ms]
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - all completed
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - some pending [114122.14ms]
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - some pending
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - error case [114122.17ms]
(fail) Database Operations > Dependency Helper Operations > should check if dependencies are met - error case
(fail) Database Operations > Dependency Helper Operations > should handle no dependencies [114122.20ms]
(fail) Database Operations > Dependency Helper Operations > should handle no dependencies
(fail) Database Operations > Dependency Helper Operations > should remove dependencies [114122.23ms]
(fail) Database Operations > X  emove dependencies
(fail) Database Operations > Media Table Operations > should insert and retrieve media records [0.09ms]
(fail) Database Operations > Media Table Operations > should handle duplicate video_id constraint [0.06ms]
(fail) Database Operations > Complex Queries > should query tasks with their dependencies [114122.47ms]
(fail) Database Operations > Complex Queries > should query tasks with their dependencies
(fail) Database Operations > Complex Queries > should query parent-child relationships [114122.51ms]
(fail) Database Operations > Complex Queries > should query parent-child relationships
(fail) Database Operations > Complex Queries > should count tasks by status [114122.55ms]
(fail) Database Operations >  	X  ount tasks by status
(fail) Database Operations > Transaction Handling > should handle successful transactions [0.09ms]
(fail) Database Operations > Transaction Handling > should handle transaction rollback [0.09ms]
(fail) parseCliArgs > parses required media id and defaults [114133.65ms]
(fail) parseCliArgs > parses optional style [0.11ms]
(fail) parseCliArgs > throws on invalid style [0.05ms]
(fail) parseCliArgs > throws when media id missing [0.04ms]
(fail) parseCliArgs > prints help when requested [0.12ms]
(fail) validateMediaExists > detects existing media with transcript [0.21ms]
(fail) validateMediaExists > detects media without transcript [0.19ms]
(fail) validateMediaExists > handles missing media [0.15ms]
(fail) runDirectSummarization > outputs summary info [0.18ms]
(fail) runDirectSummarization > handles failure result [0.29ms]
(fail) createSummarizationTask > creates task through executor [0.25ms]
(fail) Service Integration > should integrate planner and review services [0.26ms]
(fail) Service Integration > should handle service errors gracefully [0.20ms]
(fail) Task Executors > Shell Executor > should execute simple shell command successfully [6.83ms]
(fail) Task Executors > Shell Executor > should handle shell command failure [2.29ms]
(fail) Task Executors > Shell Executor > should handle complex shell commands [3.89ms]
(fail) Task Executors > LLM Executor > should execute LLM task successfully [2.46ms]
(fail) Task Executors > Code Executor > should execute code generation task [1.09ms]
(fail) Task Executors > Code Executor > should infer language from description [0.42ms]
(fail) Task Executors > Tool Executor > should handle missing tool [0.17ms]
(fail) Task Executors > Task Dispatcher > should dispatch shell task correctly [0.19ms]
(fail) Task Executors > Task Dispatcher > should dispatch LLM task correctly [0.16ms]
(fail) Task Executors > Task Dispatcher > should dispatch tool task correctly [0.15ms]
(fail) Task Executors > Task Dispatcher > should handle unknown task type [0.15ms]
(fail) Content Quality Server > track_quality_improvements > should identify improvement areas [0.39ms]
(fail) File Processing Workflow > Task File Parsing > should parse YAML task file with frontmatter [0.63ms]
(fail) File Processing Workflow > Task File Parsing > should parse JSON task file [0.37ms]
(fail) File Processing Workflow > Task File Parsing > should parse Markdown file with YAML frontmatter [0.34ms]
(fail) File Processing Workflow > Task File Parsing > should handle complex tool task with nested args [0.32ms]
(fail) File Processing Workflow > Task File Parsing > should handle batch task with generator [0.33ms]
(fail) File Processing Workflow > Task File Parsing > should handle invalid YAML gracefully [0.28ms]
(fail) File Processing Workflow > Task File Parsing > should handle missing required fields [0.29ms]
(fail) File Processing Workflow > File Hashing > should generate different hashes for different content [0.36ms]
(fail) File Processing Workflow > Task Conversion > should convert database tasks to base tasks [0.21ms]
(fail) File Processing Workflow > Task Conversion > should handle tasks with JSON fields [0.16ms]
(fail) File Processing Workflow > Task Conversion > should handle malformed JSON gracefully [0.15ms]
(fail) File Processing Workflow > File Processing Integration > should process complete workflow from file to database [0.48ms]
(fail) hashFile utility > generates expected SHA-256 hash for known content [0.38ms]
(fail) ToolRunner Basic Tools > reads files using read_file [0.29ms]
(fail) ToolRunner Basic Tools > writes files using write_file [0.18ms]
(fail) ToolRunner Basic Tools > throws for unknown tool [0.13ms]
(fail) Resource Optimization Server > analyze_resource_usage > should calculate optimization score [0.43ms]
(fail) Resource Optimization Server > optimize_load_balancing > should support different optimization strategies [0.71ms]
(fail) Media Executor > executeMediaIngestTask > should process file when force flag is set [0.88ms]
--------------------------------------------------|---------|---------|-------------------
File                                              | % Funcs | % Lines | Uncovered Line #s
--------------------------------------------------|---------|---------|-------------------
All files                                         |   63.14 |   56.63 |
 src/autolearn-agent.ts                           |   94.74 |  100.00 | 
 src/cli/analyze-cross-modal-intelligence.ts      |   86.36 |   73.75 | 43-44,52-53,70-71,127-170,180-181,216-217,244-245,288-289,361-362,366-367
 src/cli/smart-transcribe.ts                      |   50.00 |   17.60 | 42-80,135-169,174-241,246-275,280-321,326-370,375-416,421-470,475-503,538-540
 src/config.ts                                    |  100.00 |  100.00 | 
 src/db.ts                                        |    0.00 |    2.94 | 9-264,269-272,276-279
 src/executors/code.ts                            |  100.00 |   78.18 | 12-17,19,25,47-49
 src/executors/llm.ts                             |  100.00 |  100.00 | 
 src/executors/media.ts                           |   30.77 |   15.96 | 24-26,51,63-64,66,77,80-81,83-90,93,96-99,102-104,107-109,111-119,156-163,165-166,168-173,186-231,236-302,306-328,335-474,482-520,528-566,571-589,593-611,615-621
 src/executors/shell.ts                           |  100.00 |   88.24 | 35-38
 src/executors/tool.ts                            |  100.00 |  100.00 | 
 src/mcp/content-quality-server.ts                |   23.53 |   24.76 | 210-236,239-312,316-322,326-330,334-391,395-489,493-498,503-508,513-518,523-571,575-593,597-669,673-688,692-771,775-782,786-792,796-802,806-821
 src/mcp/enhanced-task-processor.ts               |   47.37 |   19.03 | 59-64,66-327
 src/mcp/mcp-client.ts                            |    0.00 |    0.84 | 31-125,130-1095
 src/mcp/metadata-optimization-server.ts          |   45.45 |   29.15 | 222-326,330-499,504-680
 src/mcp/pattern-analysis-server.ts               |   30.77 |   25.14 | 199-225,228-391,395-412,416-430,434-552,556-564,569-589,593-603,607-755
 src/mcp/resource-optimization-server.ts          |   29.63 |   26.41 | 203-229,232-350,354-432,436-444,448-487,491-511,515-519,523-615,619-665,669-724
 src/mcp/user-behavior-server.ts                  |   28.57 |   26.32 | 207-233,236-476,480-544,548-621,625-633,637-706,710-724
 src/migrations/001-normalize-dependencies.ts     |    0.00 |    5.22 | 30-230,242,249-255,262-268,275-278,285-288,295-302,309-330
 src/migrations/002-add-retry-system.ts           |   93.75 |   84.02 | 57-91
 src/migrations/003-add-periodic-tasks.ts         |   93.33 |   76.88 | 54-90
 src/scheduler/cron-parser.ts                     |   80.00 |  100.00 | 
 src/scheduler/task-scheduler.ts                  |   33.33 |   18.09 | 38-40,138-460
 src/services/content-engagement-service.ts       |   71.43 |   70.50 | 383-422,429-438,445-471
 src/services/cross-modal-intelligence-service.ts |   86.11 |   74.79 | 747-838,845-897,1038-1044
 src/services/llm-planning-service.ts             |   35.71 |   13.62 | 22-269,303-375,444-459,498-718
 src/services/planner-service.ts                  |   88.89 |   98.80 | 
 src/services/resource-optimizer-service.ts       |   56.25 |   21.79 | 36-270,370-407,414-416,420-422,426-428,432-540,579-584,618-630,639-673
 src/services/review-service.ts                   |   85.71 |  100.00 | 
 src/tools/tool_runner.ts                         |    0.00 |    5.49 | 114-115,119-120,124-257,261-268,272-294,298-442,447-461,465-489,494-518,522-529,533-539,543-555,559-560,564-565,569-570,576-592
 src/types/periodic.ts                            |  100.00 |  100.00 | 
 src/types/rule-scheduler.ts                      |  100.00 |  100.00 | 
 src/utils/cross-platform-paths.ts                |   81.25 |   66.97 | 16,34,36-37,52,58,60-61,73-77,150,170-174,193-209
 src/utils/filename_normalizer.ts                 |   88.89 |   67.61 | 47-51,114,151-153,177,187-222
 src/utils/logger.ts                              |    0.00 |    8.20 | 10-65
 src/utils/media_organizer.ts                     |  100.00 |   92.04 | 89-93,175-181,183-188
 src/utils/media_type_detector.ts                 |   85.71 |   79.43 | 30-42,102-106,115-120,122-128,130-134
 src/utils/parent_task_utils.ts                   |  100.00 |   98.00 | 
 src/validation/schemas.ts                        |   53.85 |   45.37 | 38-39,43-44,48-70,79,103-107,112-114,135,139,143,147,151,156-164,177,185,196,206-217,226,230,236-251,255-270,274-297,306,318,324-341,345-369,376,384,392,396-407
 src/validation/type-guards.ts                    |   61.11 |   53.45 | 61-63,67-69,73-75,79-81,85-87,97-99,103-105,114,118,127-144,174-189,234,239,248,253,257,263-280
--------------------------------------------------|---------|---------|-------------------

 289 <USER>
 <GROUP> fail
 109 errors
 970 expect() calls
Ran 577 tests across 52 files. [1.67s]
